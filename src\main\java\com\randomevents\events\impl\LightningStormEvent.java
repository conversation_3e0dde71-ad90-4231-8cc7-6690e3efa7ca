package com.randomevents.events.impl;

import com.randomevents.events.RandomEvent;
import net.minecraft.entity.EntityType;
import net.minecraft.entity.effect.LightningBoltEntity;
import net.minecraft.entity.player.ServerPlayerEntity;
import net.minecraft.util.math.BlockPos;
import net.minecraft.world.server.ServerWorld;

import java.util.concurrent.ThreadLocalRandom;

public class LightningStormEvent extends RandomEvent {
    
    public LightningStormEvent() {
        super("lightning_storm", "Lightning Storm", "Lightning strikes around you!", EventType.BAD, 5);
    }
    
    @Override
    public void execute(ServerPlayerEntity player, ServerWorld world) {
        BlockPos playerPos = player.blockPosition();
        int lightningCount = ThreadLocalRandom.current().nextInt(3, 7); // 3-6 lightning bolts
        
        for (int i = 0; i < lightningCount; i++) {
            // Strike lightning in a radius around the player
            double angle = ThreadLocalRandom.current().nextDouble() * 2 * Math.PI;
            double radius = ThreadLocalRandom.current().nextDouble(5, 15);
            
            double x = playerPos.getX() + Math.cos(angle) * radius;
            double z = playerPos.getZ() + Math.sin(angle) * radius;
            double y = playerPos.getY() + 10; // Strike above player
            
            LightningBoltEntity lightning = EntityType.LIGHTNING_BOLT.create(world);
            if (lightning != null) {
                lightning.moveTo(x, y, z);
                world.addFreshEntity(lightning);
            }
        }
    }
}
