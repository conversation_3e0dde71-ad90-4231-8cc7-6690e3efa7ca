package com.randomevents.client;

import com.randomevents.RandomEventsMod;
import com.randomevents.client.gui.RandomEventsScreen;
import net.minecraft.client.Minecraft;
import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.api.distmarker.OnlyIn;
import net.minecraftforge.event.TickEvent;
import net.minecraftforge.eventbus.api.SubscribeEvent;

@OnlyIn(Dist.CLIENT)
public class ClientEventHandler {
    
    @SubscribeEvent
    public void onClientTick(TickEvent.ClientTickEvent event) {
        if (event.phase == TickEvent.Phase.END) {
            Minecraft mc = Minecraft.getInstance();
            
            // Check if the GUI key is pressed
            if (RandomEventsMod.openGuiKey.consumeClick()) {
                if (mc.screen == null) {
                    mc.setScreen(new RandomEventsScreen());
                }
            }
        }
    }
}
