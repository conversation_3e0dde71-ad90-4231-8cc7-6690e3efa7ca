package com.randomevents.events.impl;

import com.randomevents.events.RandomEvent;
import net.minecraft.entity.player.ServerPlayerEntity;
import net.minecraft.potion.EffectInstance;
import net.minecraft.potion.Effects;
import net.minecraft.util.text.StringTextComponent;
import net.minecraft.util.text.TextFormatting;
import net.minecraft.world.server.ServerWorld;

import java.util.List;

public class PhantomZoneEvent extends RandomEvent {
    
    public PhantomZoneEvent() {
        super("phantom_zone", "Phantom Zone", "You randomly phase in and out of reality!", EventType.BAD, 5);
    }
    
    @Override
    public void execute(ServerPlayerEntity player, ServerWorld world) {
        List<ServerPlayerEntity> allPlayers = world.getServer().getPlayerList().getPlayers();
        
        for (ServerPlayerEntity p : allPlayers) {
            p.sendMessage(
                new StringTextComponent("PHANTOM ZONE: You're phasing in and out of reality for 35 seconds!")
                    .withStyle(TextFormatting.DARK_BLUE), 
                p.getUUID()
            );
            
            // Add spectator-like effects periodically
            // This is a simplified version - in a full implementation you'd need custom logic
            // to randomly toggle spectator mode or noclip
            p.addEffect(new EffectInstance(Effects.INVISIBILITY, 700, 0)); // 35 seconds
            p.addEffect(new EffectInstance(Effects.MOVEMENT_SPEED, 700, 1)); // Faster movement
            
            p.sendMessage(
                new StringTextComponent("You feel yourself becoming incorporeal...")
                    .withStyle(TextFormatting.GRAY, TextFormatting.ITALIC), 
                p.getUUID()
            );
        }
    }
}
