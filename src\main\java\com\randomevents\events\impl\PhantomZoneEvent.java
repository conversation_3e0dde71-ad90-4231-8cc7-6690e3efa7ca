package com.randomevents.events.impl;

import com.randomevents.events.RandomEvent;
import net.minecraft.entity.player.ServerPlayerEntity;
import net.minecraft.potion.EffectInstance;
import net.minecraft.potion.Effects;
import net.minecraft.util.text.StringTextComponent;
import net.minecraft.util.text.TextFormatting;
import net.minecraft.world.server.ServerWorld;

import java.util.List;

public class PhantomZoneEvent extends RandomEvent {
    
    public PhantomZoneEvent() {
        super("phantom_zone", "Phantom Zone", "You randomly phase in and out of reality!", EventType.BAD, 5);
    }
    
    @Override
    public void execute(ServerPlayerEntity player, ServerWorld world) {
        List<ServerPlayerEntity> allPlayers = world.getServer().getPlayerList().getPlayers();
        
        for (ServerPlayerEntity p : allPlayers) {
            // Show title at top of screen
            p.connection.send(new net.minecraft.network.play.server.STitlePacket(
                net.minecraft.network.play.server.STitlePacket.Type.TITLE,
                new StringTextComponent("Phantom Zone").withStyle(TextFormatting.DARK_BLUE)
            ));

            // Add spectator-like effects
            p.addEffect(new EffectInstance(Effects.INVISIBILITY, 600, 0)); // 30 seconds
            p.addEffect(new EffectInstance(Effects.MOVEMENT_SPEED, 600, 1)); // Faster movement
        }
    }
}
