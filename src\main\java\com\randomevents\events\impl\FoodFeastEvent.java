package com.randomevents.events.impl;

import com.randomevents.events.RandomEvent;
import net.minecraft.entity.player.ServerPlayerEntity;
import net.minecraft.item.ItemStack;
import net.minecraft.item.Items;
import net.minecraft.world.server.ServerWorld;

import java.util.concurrent.ThreadLocalRandom;

public class FoodFeastEvent extends RandomEvent {
    
    public FoodFeastEvent() {
        super("food_feast", "Food Feast", "Receive a variety of delicious food!", EventType.GOOD, 9);
    }
    
    @Override
    public void execute(ServerPlayerEntity player, ServerWorld world) {
        // Give various food items
        ItemStack[] foods = {
            new ItemStack(Items.COOKED_BEEF, ThreadLocalRandom.current().nextInt(8, 16)),
            new ItemStack(Items.BREAD, ThreadLocalRandom.current().nextInt(12, 20)),
            new ItemStack(Items.COOKED_PORKCHOP, ThreadLocalRandom.current().nextInt(6, 12)),
            new ItemStack(Items.CAKE, ThreadLocalRandom.current().nextInt(2, 4)),
            new ItemStack(Items.GOLDEN_CARROT, ThreadLocalRandom.current().nextInt(4, 8))
        };
        
        for (ItemStack food : foods) {
            if (!player.addItem(food)) {
                player.drop(food, false);
            }
        }
        
        // Also restore hunger and saturation
        player.getFoodData().setFoodLevel(20);
        player.getFoodData().setSaturation(20.0f);
    }
}
