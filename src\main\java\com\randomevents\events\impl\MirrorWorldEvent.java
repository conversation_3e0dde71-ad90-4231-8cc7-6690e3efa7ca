package com.randomevents.events.impl;

import com.randomevents.events.RandomEvent;
import net.minecraft.entity.player.ServerPlayerEntity;
import net.minecraft.potion.EffectInstance;
import net.minecraft.potion.Effects;
import net.minecraft.util.math.vector.Vector3d;
import net.minecraft.util.text.StringTextComponent;
import net.minecraft.util.text.TextFormatting;
import net.minecraft.world.server.ServerWorld;
import net.minecraftforge.common.MinecraftForge;
import net.minecraftforge.event.TickEvent;
import net.minecraftforge.eventbus.api.SubscribeEvent;

import java.util.*;

public class MirrorWorldEvent extends RandomEvent {
    private static final Map<UUID, MirrorWorldInstance> activeInstances = new HashMap<>();

    public MirrorWorldEvent() {
        super("mirror_world", "Mirror World", "Your movement is completely reversed!", EventType.BAD, 7);
    }

    @Override
    public void execute(ServerPlayerEntity player, ServerWorld world) {
        List<ServerPlayerEntity> allPlayers = world.getServer().getPlayerList().getPlayers();

        for (ServerPlayerEntity p : allPlayers) {
            // Show title at top of screen
            p.connection.send(new net.minecraft.network.play.server.STitlePacket(
                net.minecraft.network.play.server.STitlePacket.Type.TITLE,
                new StringTextComponent("Mirror World").withStyle(TextFormatting.LIGHT_PURPLE)
            ));

            // Add nausea and slowness for disorientation
            p.addEffect(new EffectInstance(Effects.CONFUSION, 600, 1)); // 30 seconds
            p.addEffect(new EffectInstance(Effects.MOVEMENT_SLOWDOWN, 600, 0)); // 30 seconds

            MirrorWorldInstance instance = new MirrorWorldInstance(p, world);
            activeInstances.put(p.getUUID(), instance);
        }

        // Register the tick handler if not already registered
        if (!activeInstances.isEmpty()) {
            MinecraftForge.EVENT_BUS.register(new MirrorWorldTickHandler());
        }
    }

    public static class MirrorWorldInstance {
        private final UUID playerUUID;
        private final ServerWorld world;
        private int ticksRemaining;
        private Vector3d lastPosition;

        public MirrorWorldInstance(ServerPlayerEntity player, ServerWorld world) {
            this.playerUUID = player.getUUID();
            this.world = world;
            this.ticksRemaining = 600; // 30 seconds
            this.lastPosition = player.position();
        }

        public boolean tick() {
            ticksRemaining--;

            ServerPlayerEntity player = world.getServer().getPlayerList().getPlayer(playerUUID);
            if (player == null || !player.isAlive()) {
                return false;
            }

            if (ticksRemaining <= 0) {
                return false;
            }

            // Reverse player movement
            Vector3d currentPos = player.position();
            Vector3d movement = currentPos.subtract(lastPosition);

            if (movement.lengthSqr() > 0.001) { // Player is moving
                // Reverse the movement direction
                Vector3d reversedMovement = movement.scale(-0.8); // Reverse and slightly reduce
                Vector3d newPos = lastPosition.add(reversedMovement);

                // Teleport player to reversed position
                player.teleportTo(newPos.x, newPos.y, newPos.z);
            }

            lastPosition = player.position();
            return true;
        }
    }

    public static class MirrorWorldTickHandler {
        @SubscribeEvent
        public void onServerTick(TickEvent.ServerTickEvent event) {
            if (event.phase != TickEvent.Phase.END) return;

            Iterator<Map.Entry<UUID, MirrorWorldInstance>> iterator = activeInstances.entrySet().iterator();

            while (iterator.hasNext()) {
                Map.Entry<UUID, MirrorWorldInstance> entry = iterator.next();

                if (!entry.getValue().tick()) {
                    iterator.remove();
                }
            }

            // Unregister if no active instances
            if (activeInstances.isEmpty()) {
                MinecraftForge.EVENT_BUS.unregister(this);
            }
        }
    }
}
