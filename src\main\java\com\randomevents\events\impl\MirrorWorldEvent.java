package com.randomevents.events.impl;

import com.randomevents.events.RandomEvent;
import net.minecraft.entity.player.ServerPlayerEntity;
import net.minecraft.potion.EffectInstance;
import net.minecraft.potion.Effects;
import net.minecraft.util.text.StringTextComponent;
import net.minecraft.util.text.TextFormatting;
import net.minecraft.world.server.ServerWorld;

import java.util.List;

public class MirrorWorldEvent extends RandomEvent {
    
    public MirrorWorldEvent() {
        super("mirror_world", "Mirror World", "Everything is backwards! Left is right, forward is back!", EventType.BAD, 7);
    }
    
    @Override
    public void execute(ServerPlayerEntity player, ServerWorld world) {
        List<ServerPlayerEntity> allPlayers = world.getServer().getPlayerList().getPlayers();
        
        for (ServerPlayerEntity p : allPlayers) {
            p.sendMessage(
                new StringTextComponent("MIRROR WORLD: Your controls are reversed for 30 seconds!")
                    .withStyle(TextFormatting.LIGHT_PURPLE), 
                p.getUUID()
            );
            
            // Add nausea to make it more disorienting
            p.addEffect(new EffectInstance(Effects.CONFUSION, 600, 1)); // 30 seconds
            
            // Add slowness to make the reversed controls more manageable
            p.addEffect(new EffectInstance(Effects.MOVEMENT_SLOWDOWN, 600, 0)); // 30 seconds
        }
    }
}
