package com.randomevents.events.impl;

import com.randomevents.events.RandomEvent;
import net.minecraft.entity.EntityType;
import net.minecraft.entity.monster.ZombieEntity;
import net.minecraft.entity.player.ServerPlayerEntity;
import net.minecraft.util.math.BlockPos;
import net.minecraft.world.server.ServerWorld;

import java.util.concurrent.ThreadLocalRandom;

public class ZombieSwarmEvent extends RandomEvent {
    
    public ZombieSwarmEvent() {
        super("zombie_swarm", "Zombie Swarm", "A horde of zombies appears around you!", EventType.BAD, 8);
    }
    
    @Override
    public void execute(ServerPlayerEntity player, ServerWorld world) {
        BlockPos playerPos = player.blockPosition();
        int zombieCount = ThreadLocalRandom.current().nextInt(5, 10); // 5-9 zombies
        
        for (int i = 0; i < zombieCount; i++) {
            // Spawn zombies in a circle around the player
            double angle = (2 * Math.PI * i) / zombieCount;
            double radius = ThreadLocalRandom.current().nextDouble(8, 15);
            
            double x = playerPos.getX() + Math.cos(angle) * radius;
            double z = playerPos.getZ() + Math.sin(angle) * radius;
            double y = playerPos.getY();
            
            ZombieEntity zombie = new ZombieEntity(EntityType.ZOMBIE, world);
            zombie.setPos(x, y, z);
            zombie.setTarget(player);
            
            // Make some zombies stronger
            if (ThreadLocalRandom.current().nextDouble() < 0.3) { // 30% chance
                zombie.setBaby(false);
                zombie.getAttribute(net.minecraft.entity.ai.attributes.Attributes.MAX_HEALTH).setBaseValue(30.0);
                zombie.setHealth(30.0f);
            }
            
            world.addFreshEntity(zombie);
        }
    }
}
