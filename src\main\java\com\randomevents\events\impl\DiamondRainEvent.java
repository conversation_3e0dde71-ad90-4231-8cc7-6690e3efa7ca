package com.randomevents.events.impl;

import com.randomevents.events.RandomEvent;
import net.minecraft.entity.item.ItemEntity;
import net.minecraft.entity.player.ServerPlayerEntity;
import net.minecraft.item.ItemStack;
import net.minecraft.item.Items;
import net.minecraft.util.math.BlockPos;
import net.minecraft.world.server.ServerWorld;

import java.util.concurrent.ThreadLocalRandom;

public class DiamondRainEvent extends RandomEvent {
    
    public DiamondRainEvent() {
        super("diamond_rain", "Diamond Rain", "Diamonds fall from the sky around you!", EventType.GOOD, 5);
    }
    
    @Override
    public void execute(ServerPlayerEntity player, ServerWorld world) {
        BlockPos playerPos = player.blockPosition();
        int diamondCount = ThreadLocalRandom.current().nextInt(3, 8); // 3-7 diamonds
        
        for (int i = 0; i < diamondCount; i++) {
            // Spawn diamonds in a 10x10 area around the player
            double x = playerPos.getX() + ThreadLocalRandom.current().nextDouble(-5, 5);
            double y = playerPos.getY() + ThreadLocalRandom.current().nextDouble(5, 15);
            double z = playerPos.getZ() + ThreadLocalRandom.current().nextDouble(-5, 5);
            
            ItemStack diamond = new ItemStack(Items.DIAMOND);
            ItemEntity diamondEntity = new ItemEntity(world, x, y, z, diamond);
            
            // Add some random velocity
            diamondEntity.setDeltaMovement(
                ThreadLocalRandom.current().nextDouble(-0.2, 0.2),
                ThreadLocalRandom.current().nextDouble(-0.1, 0.1),
                ThreadLocalRandom.current().nextDouble(-0.2, 0.2)
            );
            
            world.addFreshEntity(diamondEntity);
        }
    }
}
