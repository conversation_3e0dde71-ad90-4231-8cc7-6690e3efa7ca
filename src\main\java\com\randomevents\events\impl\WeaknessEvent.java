package com.randomevents.events.impl;

import com.randomevents.events.RandomEvent;
import net.minecraft.entity.player.ServerPlayerEntity;
import net.minecraft.potion.EffectInstance;
import net.minecraft.potion.Effects;
import net.minecraft.world.server.ServerWorld;

public class WeaknessEvent extends RandomEvent {
    
    public WeaknessEvent() {
        super("weakness", "Weakness", "Your attacks become much weaker!", EventType.BAD, 9);
    }
    
    @Override
    public void execute(ServerPlayerEntity player, ServerWorld world) {
        // Weakness II for 3 minutes (3600 ticks)
        EffectInstance weakness = new EffectInstance(Effects.WEAKNESS, 3600, 1);
        player.addEffect(weakness);
        
        // Also add mining fatigue
        EffectInstance miningFatigue = new EffectInstance(Effects.DIG_SLOWDOWN, 3600, 1);
        player.addEffect(miningFatigue);
    }
}
