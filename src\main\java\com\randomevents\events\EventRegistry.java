package com.randomevents.events;

import com.randomevents.events.impl.*;

public class EventRegistry {
    
    public static void registerAllEvents(RandomEventManager manager) {
        // Good Events
        manager.registerEvent(new DiamondRainEvent());
        manager.registerEvent(new GoldenAppleGiftEvent());
        manager.registerEvent(new SpeedBoostEvent());
        manager.registerEvent(new LuckyFindEvent());
        manager.registerEvent(new HealthBoostEvent());
        manager.registerEvent(new ExperienceShowerEvent());
        manager.registerEvent(new EnchantedToolEvent());
        manager.registerEvent(new FoodFeastEvent());
        manager.registerEvent(new FireResistanceEvent());
        manager.registerEvent(new NightVisionEvent());
        
        // Bad Events
        manager.registerEvent(new ZombieSwarmEvent());
        manager.registerEvent(new SkeletonAmbushEvent());
        manager.registerEvent(new CreeperSurpriseEvent());
        manager.registerEvent(new HungerCurseEvent());
        manager.registerEvent(new SlownessEvent());
        manager.registerEvent(new BlindnessEvent());
        manager.registerEvent(new PoisonCloudEvent());
        manager.registerEvent(new WeaknessEvent());
        manager.registerEvent(new InventoryScrambleEvent());
        manager.registerEvent(new LightningStormEvent());
    }
}
