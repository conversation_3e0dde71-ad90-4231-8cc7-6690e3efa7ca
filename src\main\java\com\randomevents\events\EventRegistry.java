package com.randomevents.events;

import com.randomevents.config.RandomEventsConfig;
import com.randomevents.events.impl.*;

public class EventRegistry {

    public static void registerAllEvents(RandomEventManager manager) {
        // Unique Events (mostly challenging/bad)
        if (RandomEventsConfig.ENABLE_BAD_EVENTS.get()) {
            manager.registerEvent(new CreeperSurpriseEvent());
            manager.registerEvent(new StayTogetherEvent());
            manager.registerEvent(new GravityFlipEvent());
            manager.registerEvent(new MirrorWorldEvent());
            manager.registerEvent(new BlockSwapEvent());
            manager.registerEvent(new ShadowCloneEvent());
            manager.registerEvent(new InventoryRouletteEvent());
            manager.registerEvent(new PhantomZoneEvent());
        }
    }
}
