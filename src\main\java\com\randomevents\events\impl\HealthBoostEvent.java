package com.randomevents.events.impl;

import com.randomevents.events.RandomEvent;
import net.minecraft.entity.player.ServerPlayerEntity;
import net.minecraft.potion.EffectInstance;
import net.minecraft.potion.Effects;
import net.minecraft.world.server.ServerWorld;

public class HealthBoostEvent extends RandomEvent {
    
    public HealthBoostEvent() {
        super("health_boost", "Health Boost", "Gain extra health and regeneration!", EventType.GOOD, 9);
    }
    
    @Override
    public void execute(ServerPlayerEntity player, ServerWorld world) {
        // Health Boost II for 5 minutes (6000 ticks) - adds 4 extra hearts
        EffectInstance healthBoost = new EffectInstance(Effects.HEALTH_BOOST, 6000, 1);
        player.addEffect(healthBoost);
        
        // Regeneration II for 30 seconds (600 ticks)
        EffectInstance regeneration = new EffectInstance(Effects.REGENERATION, 600, 1);
        player.addEffect(regeneration);
        
        // Heal the player to full health
        player.heal(player.getMaxHealth());
    }
}
