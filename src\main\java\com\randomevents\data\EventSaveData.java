package com.randomevents.data;

import com.randomevents.RandomEventsMod;
import net.minecraft.nbt.CompoundNBT;
import net.minecraft.world.server.ServerWorld;
import net.minecraft.world.storage.WorldSavedData;

public class EventSaveData extends WorldSavedData {
    private static final String DATA_NAME = RandomEventsMod.MOD_ID + "_event_data";
    
    private boolean isRunning = false;
    private int eventInterval = 300; // 5 minutes default
    private int currentTick = 0;
    private int nextEventTick = 0;
    private boolean manualEventMode = false;
    private int currentEventIndex = -1;
    
    public EventSaveData() {
        super(DATA_NAME);
    }
    
    public EventSaveData(String name) {
        super(name);
    }
    
    public static EventSaveData get(ServerWorld world) {
        return world.getDataStorage().computeIfAbsent(EventSaveData::new, DATA_NAME);
    }
    
    @Override
    public void load(CompoundNBT nbt) {
        isRunning = nbt.getBoolean("isRunning");
        eventInterval = nbt.getInt("eventInterval");
        currentTick = nbt.getInt("currentTick");
        nextEventTick = nbt.getInt("nextEventTick");
        manualEventMode = nbt.getBoolean("manualEventMode");
        currentEventIndex = nbt.getInt("currentEventIndex");
        
        RandomEventsMod.LOGGER.info("Loaded event save data - Running: " + isRunning + ", Interval: " + eventInterval);
    }
    
    @Override
    public CompoundNBT save(CompoundNBT nbt) {
        nbt.putBoolean("isRunning", isRunning);
        nbt.putInt("eventInterval", eventInterval);
        nbt.putInt("currentTick", currentTick);
        nbt.putInt("nextEventTick", nextEventTick);
        nbt.putBoolean("manualEventMode", manualEventMode);
        nbt.putInt("currentEventIndex", currentEventIndex);
        
        return nbt;
    }
    
    // Getters and setters
    public boolean isRunning() {
        return isRunning;
    }
    
    public void setRunning(boolean running) {
        this.isRunning = running;
        setDirty();
    }
    
    public int getEventInterval() {
        return eventInterval;
    }
    
    public void setEventInterval(int eventInterval) {
        this.eventInterval = eventInterval;
        setDirty();
    }
    
    public int getCurrentTick() {
        return currentTick;
    }
    
    public void setCurrentTick(int currentTick) {
        this.currentTick = currentTick;
        setDirty();
    }
    
    public int getNextEventTick() {
        return nextEventTick;
    }
    
    public void setNextEventTick(int nextEventTick) {
        this.nextEventTick = nextEventTick;
        setDirty();
    }
    
    public boolean isManualEventMode() {
        return manualEventMode;
    }
    
    public void setManualEventMode(boolean manualEventMode) {
        this.manualEventMode = manualEventMode;
        setDirty();
    }
    
    public int getCurrentEventIndex() {
        return currentEventIndex;
    }
    
    public void setCurrentEventIndex(int currentEventIndex) {
        this.currentEventIndex = currentEventIndex;
        setDirty();
    }
}
