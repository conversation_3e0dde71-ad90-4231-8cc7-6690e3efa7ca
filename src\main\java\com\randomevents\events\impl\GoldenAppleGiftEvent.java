package com.randomevents.events.impl;

import com.randomevents.events.RandomEvent;
import net.minecraft.entity.player.ServerPlayerEntity;
import net.minecraft.item.ItemStack;
import net.minecraft.item.Items;
import net.minecraft.world.server.ServerWorld;

import java.util.concurrent.ThreadLocalRandom;

public class GoldenAppleGiftEvent extends RandomEvent {
    
    public GoldenAppleGiftEvent() {
        super("golden_apple_gift", "Golden Apple Gift", "Receive golden apples in your inventory!", EventType.GOOD, 8);
    }
    
    @Override
    public void execute(ServerPlayerEntity player, ServerWorld world) {
        int appleCount = ThreadLocalRandom.current().nextInt(2, 5); // 2-4 golden apples
        boolean enchanted = ThreadLocalRandom.current().nextDouble() < 0.3; // 30% chance for enchanted
        
        ItemStack apples = new ItemStack(enchanted ? Items.ENCHANTED_GOLDEN_APPLE : Items.GOLDEN_APPLE, appleCount);
        
        if (!player.addItem(apples)) {
            // If inventory is full, drop the items
            player.drop(apples, false);
        }
    }
}
