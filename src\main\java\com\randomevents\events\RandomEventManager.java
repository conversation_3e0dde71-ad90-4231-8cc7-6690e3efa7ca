package com.randomevents.events;

import com.randomevents.RandomEventsMod;
import net.minecraft.entity.player.ServerPlayerEntity;
import net.minecraft.server.MinecraftServer;
import net.minecraft.util.text.StringTextComponent;
import net.minecraft.util.text.TextFormatting;
import net.minecraft.world.server.ServerWorld;
import net.minecraftforge.common.MinecraftForge;
import net.minecraftforge.event.TickEvent;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.server.ServerLifecycleHooks;

import java.util.*;
import java.util.concurrent.ThreadLocalRandom;

public class RandomEventManager {
    private final List<RandomEvent> events = new ArrayList<>();
    private final Map<String, RandomEvent> eventMap = new HashMap<>();
    
    private boolean isRunning = false;
    private int currentTick = 0;
    private int eventInterval = 6000; // 5 minutes in ticks (20 ticks per second * 300 seconds)
    private int nextEventTick = 0;
    
    private RandomEvent currentEvent = null;
    private int currentEventIndex = -1;
    private boolean manualEventMode = false;
    
    public RandomEventManager() {
        MinecraftForge.EVENT_BUS.register(this);
        registerEvents();
        calculateNextEventTick();
    }
    
    private void registerEvents() {
        RandomEventsMod.LOGGER.info("Registering random events...");
        EventRegistry.registerAllEvents(this);
    }
    
    public void registerEvent(RandomEvent event) {
        events.add(event);
        eventMap.put(event.getId(), event);
        RandomEventsMod.LOGGER.info("Registered event: " + event.getName());
    }
    
    @SubscribeEvent
    public void onServerTick(TickEvent.ServerTickEvent event) {
        if (event.phase != TickEvent.Phase.END || !isRunning) {
            return;
        }
        
        currentTick++;
        
        if (currentTick >= nextEventTick && !events.isEmpty()) {
            executeRandomEvent();
            calculateNextEventTick();
        }
    }
    
    private void executeRandomEvent() {
        MinecraftServer server = ServerLifecycleHooks.getCurrentServer();
        if (server == null) return;
        
        List<ServerPlayerEntity> players = server.getPlayerList().getPlayers();
        if (players.isEmpty()) return;
        
        RandomEvent eventToExecute;
        
        if (manualEventMode && currentEventIndex >= 0 && currentEventIndex < events.size()) {
            eventToExecute = events.get(currentEventIndex);
        } else {
            eventToExecute = selectRandomEvent();
        }
        
        if (eventToExecute == null) return;
        
        currentEvent = eventToExecute;
        
        // Execute for all players
        for (ServerPlayerEntity player : players) {
            try {
                eventToExecute.execute(player, (ServerWorld) player.level);
                
                // Send notification to player
                player.sendMessage(
                    new StringTextComponent("Random Event: ").withStyle(TextFormatting.YELLOW)
                        .append(eventToExecute.getFormattedName()),
                    player.getUUID()
                );
            } catch (Exception e) {
                RandomEventsMod.LOGGER.error("Error executing event " + eventToExecute.getId() + " for player " + player.getName().getString(), e);
            }
        }
        
        RandomEventsMod.LOGGER.info("Executed random event: " + eventToExecute.getName());
    }
    
    private RandomEvent selectRandomEvent() {
        if (events.isEmpty()) return null;
        
        int totalWeight = events.stream().mapToInt(RandomEvent::getWeight).sum();
        int randomValue = ThreadLocalRandom.current().nextInt(totalWeight);
        
        int currentWeight = 0;
        for (RandomEvent event : events) {
            currentWeight += event.getWeight();
            if (randomValue < currentWeight) {
                return event;
            }
        }
        
        return events.get(events.size() - 1); // Fallback
    }
    
    private void calculateNextEventTick() {
        nextEventTick = currentTick + eventInterval;
    }
    
    // Public API methods for GUI
    public void start() {
        isRunning = true;
        calculateNextEventTick();
        RandomEventsMod.LOGGER.info("Random events started");
    }
    
    public void pause() {
        isRunning = false;
        RandomEventsMod.LOGGER.info("Random events paused");
    }
    
    public void setEventInterval(int seconds) {
        this.eventInterval = seconds * 20; // Convert to ticks
        if (isRunning) {
            calculateNextEventTick();
        }
    }
    
    public void setManualEvent(int eventIndex) {
        if (eventIndex >= 0 && eventIndex < events.size()) {
            this.currentEventIndex = eventIndex;
            this.manualEventMode = true;
        } else {
            this.manualEventMode = false;
            this.currentEventIndex = -1;
        }
    }
    
    public void triggerEventNow() {
        if (!events.isEmpty()) {
            executeRandomEvent();
            calculateNextEventTick();
        }
    }
    
    // Getters
    public boolean isRunning() {
        return isRunning;
    }
    
    public int getEventInterval() {
        return eventInterval / 20; // Convert back to seconds
    }
    
    public int getTimeUntilNextEvent() {
        if (!isRunning) return -1;
        return Math.max(0, (nextEventTick - currentTick) / 20); // Convert to seconds
    }
    
    public List<RandomEvent> getEvents() {
        return new ArrayList<>(events);
    }
    
    public RandomEvent getCurrentEvent() {
        return currentEvent;
    }
    
    public int getCurrentEventIndex() {
        return currentEventIndex;
    }
    
    public boolean isManualEventMode() {
        return manualEventMode;
    }
    
    public void onServerStart() {
        currentTick = 0;
        calculateNextEventTick();
    }
}
