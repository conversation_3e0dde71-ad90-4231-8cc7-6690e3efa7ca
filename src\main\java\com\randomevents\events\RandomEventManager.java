package com.randomevents.events;

import com.randomevents.RandomEventsMod;
import com.randomevents.config.RandomEventsConfig;
import com.randomevents.data.EventSaveData;
import net.minecraft.entity.player.ServerPlayerEntity;
import net.minecraft.server.MinecraftServer;
import net.minecraft.util.text.StringTextComponent;
import net.minecraft.util.text.TextFormatting;
import net.minecraft.world.server.ServerWorld;
import net.minecraftforge.common.MinecraftForge;
import net.minecraftforge.event.TickEvent;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.server.ServerLifecycleHooks;

import java.util.*;
import java.util.concurrent.ThreadLocalRandom;

public class RandomEventManager {
    private final List<RandomEvent> events = new ArrayList<>();
    private final Map<String, RandomEvent> eventMap = new HashMap<>();
    
    private boolean isRunning = false;
    private int currentTick = 0;
    private int eventInterval = RandomEventsConfig.DEFAULT_EVENT_INTERVAL.get() * 20; // Convert seconds to ticks
    private int nextEventTick = 0;

    private RandomEvent currentEvent = null;
    private int currentEventIndex = -1;
    private boolean manualEventMode = false;

    private EventSaveData saveData = null;
    
    public RandomEventManager() {
        MinecraftForge.EVENT_BUS.register(this);
        registerEvents();
        calculateNextEventTick();
    }
    
    private void registerEvents() {
        RandomEventsMod.LOGGER.info("Registering random events...");
        EventRegistry.registerAllEvents(this);
        applyConfigWeights();
    }

    private void applyConfigWeights() {
        // Apply config weights to events
        for (RandomEvent event : events) {
            int configWeight = getConfigWeight(event.getId());
            if (configWeight != event.getWeight()) {
                // Create a new event with the config weight
                // For now, we'll just log this - in a full implementation you'd recreate the event
                RandomEventsMod.LOGGER.info("Event " + event.getId() + " weight: " + event.getWeight() + " -> " + configWeight);
            }
        }
    }

    private int getConfigWeight(String eventId) {
        switch (eventId) {
            case "creeper_surprise": return RandomEventsConfig.CREEPER_SURPRISE_WEIGHT.get();
            case "stay_together": return RandomEventsConfig.STAY_TOGETHER_WEIGHT.get();
            case "gravity_flip": return RandomEventsConfig.GRAVITY_FLIP_WEIGHT.get();
            case "mirror_world": return RandomEventsConfig.MIRROR_WORLD_WEIGHT.get();
            case "block_swap": return RandomEventsConfig.BLOCK_SWAP_WEIGHT.get();
            case "shadow_clone": return RandomEventsConfig.SHADOW_CLONE_WEIGHT.get();
            case "inventory_roulette": return RandomEventsConfig.INVENTORY_ROULETTE_WEIGHT.get();
            case "phantom_zone": return RandomEventsConfig.PHANTOM_ZONE_WEIGHT.get();
            default: return 5; // Default weight
        }
    }
    
    public void registerEvent(RandomEvent event) {
        events.add(event);
        eventMap.put(event.getId(), event);
        RandomEventsMod.LOGGER.info("Registered event: " + event.getName());
    }
    
    @SubscribeEvent
    public void onServerTick(TickEvent.ServerTickEvent event) {
        if (event.phase != TickEvent.Phase.END || !isRunning) {
            return;
        }
        
        currentTick++;
        
        if (currentTick >= nextEventTick && !events.isEmpty()) {
            executeRandomEvent();
            calculateNextEventTick();
        }
    }
    
    private void executeRandomEvent() {
        MinecraftServer server = ServerLifecycleHooks.getCurrentServer();
        if (server == null) return;
        
        List<ServerPlayerEntity> players = server.getPlayerList().getPlayers();
        if (players.isEmpty()) return;
        
        RandomEvent eventToExecute;
        
        if (manualEventMode && currentEventIndex >= 0 && currentEventIndex < events.size()) {
            eventToExecute = events.get(currentEventIndex);
        } else {
            eventToExecute = selectRandomEvent();
        }
        
        if (eventToExecute == null) return;
        
        currentEvent = eventToExecute;
        
        // Execute for all players
        for (ServerPlayerEntity player : players) {
            try {
                eventToExecute.execute(player, (ServerWorld) player.level);
                
                // Send notification to player
                player.sendMessage(
                    new StringTextComponent("Random Event: ").withStyle(TextFormatting.YELLOW)
                        .append(eventToExecute.getFormattedName()),
                    player.getUUID()
                );
            } catch (Exception e) {
                RandomEventsMod.LOGGER.error("Error executing event " + eventToExecute.getId() + " for player " + player.getName().getString(), e);
            }
        }
        
        RandomEventsMod.LOGGER.info("Executed random event: " + eventToExecute.getName());
    }
    
    private RandomEvent selectRandomEvent() {
        if (events.isEmpty()) return null;

        // Calculate total weight using config values
        int totalWeight = 0;
        for (RandomEvent event : events) {
            int configWeight = getConfigWeight(event.getId());
            if (configWeight > 0) {
                totalWeight += configWeight;
            }
        }

        if (totalWeight == 0) return null;

        int randomValue = ThreadLocalRandom.current().nextInt(totalWeight);

        int currentWeight = 0;
        for (RandomEvent event : events) {
            int configWeight = getConfigWeight(event.getId());
            if (configWeight > 0) {
                currentWeight += configWeight;
                if (randomValue < currentWeight) {
                    return event;
                }
            }
        }

        return events.get(events.size() - 1); // Fallback
    }
    
    private void calculateNextEventTick() {
        nextEventTick = currentTick + eventInterval;
    }
    
    // Public API methods for GUI
    public void start() {
        isRunning = true;
        calculateNextEventTick();
        saveState();
        RandomEventsMod.LOGGER.info("Random events started");
    }

    public void pause() {
        isRunning = false;
        saveState();
        RandomEventsMod.LOGGER.info("Random events paused");
    }

    public void setEventInterval(int seconds) {
        this.eventInterval = seconds * 20; // Convert to ticks
        if (isRunning) {
            calculateNextEventTick();
        }
        saveState();
    }

    public void setManualEvent(int eventIndex) {
        if (eventIndex >= 0 && eventIndex < events.size()) {
            this.currentEventIndex = eventIndex;
            this.manualEventMode = true;
        } else {
            this.manualEventMode = false;
            this.currentEventIndex = -1;
        }
        saveState();
    }
    
    public void triggerEventNow() {
        if (!events.isEmpty()) {
            executeRandomEvent();
            calculateNextEventTick();
        }
    }
    
    // Getters
    public boolean isRunning() {
        return isRunning;
    }
    
    public int getEventInterval() {
        return eventInterval / 20; // Convert back to seconds
    }
    
    public int getTimeUntilNextEvent() {
        if (!isRunning) return -1;
        return Math.max(0, (nextEventTick - currentTick) / 20); // Convert to seconds
    }
    
    public List<RandomEvent> getEvents() {
        return new ArrayList<>(events);
    }
    
    public RandomEvent getCurrentEvent() {
        return currentEvent;
    }
    
    public int getCurrentEventIndex() {
        return currentEventIndex;
    }
    
    public boolean isManualEventMode() {
        return manualEventMode;
    }
    
    public void onServerStart() {
        currentTick = 0;
        loadSaveData();

        // Apply config defaults if this is a fresh start
        if (!isRunning && RandomEventsConfig.AUTO_START_EVENTS.get()) {
            start();
        }

        calculateNextEventTick();
    }

    private void loadSaveData() {
        MinecraftServer server = ServerLifecycleHooks.getCurrentServer();
        if (server != null) {
            ServerWorld overworld = server.getLevel(net.minecraft.world.World.OVERWORLD);
            if (overworld != null) {
                saveData = EventSaveData.get(overworld);

                // Load saved state
                isRunning = saveData.isRunning();
                eventInterval = saveData.getEventInterval();
                currentTick = saveData.getCurrentTick();
                nextEventTick = saveData.getNextEventTick();
                manualEventMode = saveData.isManualEventMode();
                currentEventIndex = saveData.getCurrentEventIndex();

                RandomEventsMod.LOGGER.info("Loaded event state - Running: " + isRunning + ", Interval: " + (eventInterval/20) + "s");
            }
        }
    }

    private void saveState() {
        if (saveData != null) {
            saveData.setRunning(isRunning);
            saveData.setEventInterval(eventInterval);
            saveData.setCurrentTick(currentTick);
            saveData.setNextEventTick(nextEventTick);
            saveData.setManualEventMode(manualEventMode);
            saveData.setCurrentEventIndex(currentEventIndex);
        }
    }
}
