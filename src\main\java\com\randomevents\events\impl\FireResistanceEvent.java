package com.randomevents.events.impl;

import com.randomevents.events.RandomEvent;
import net.minecraft.entity.player.ServerPlayerEntity;
import net.minecraft.potion.EffectInstance;
import net.minecraft.potion.Effects;
import net.minecraft.world.server.ServerWorld;

public class FireResistanceEvent extends RandomEvent {
    
    public FireResistanceEvent() {
        super("fire_resistance", "Fire Resistance", "Become immune to fire and lava damage!", EventType.GOOD, 7);
    }
    
    @Override
    public void execute(ServerPlayerEntity player, ServerWorld world) {
        // Fire Resistance for 8 minutes (9600 ticks)
        EffectInstance fireResistance = new EffectInstance(Effects.FIRE_RESISTANCE, 9600, 0);
        player.addEffect(fireResistance);
    }
}
