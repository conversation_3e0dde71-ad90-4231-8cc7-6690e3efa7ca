package com.randomevents.events.impl;

import com.randomevents.events.RandomEvent;
import net.minecraft.enchantment.Enchantments;
import net.minecraft.entity.player.ServerPlayerEntity;
import net.minecraft.item.ItemStack;
import net.minecraft.item.Items;
import net.minecraft.world.server.ServerWorld;

import java.util.concurrent.ThreadLocalRandom;

public class EnchantedToolEvent extends RandomEvent {
    
    public EnchantedToolEvent() {
        super("enchanted_tool", "Enchanted Tool", "Receive a randomly enchanted tool!", EventType.GOOD, 6);
    }
    
    @Override
    public void execute(ServerPlayerEntity player, ServerWorld world) {
        ItemStack tool;
        
        // Randomly select a tool type
        double chance = ThreadLocalRandom.current().nextDouble();
        if (chance < 0.25) {
            tool = new ItemStack(Items.DIAMOND_PICKAXE);
            tool.enchant(Enchantments.BLOCK_EFFICIENCY, ThreadLocalRandom.current().nextInt(3, 6));
            if (ThreadLocalRandom.current().nextBoolean()) {
                tool.enchant(Enchantments.UNBREAKING, ThreadLocalRandom.current().nextInt(2, 4));
            }
        } else if (chance < 0.5) {
            tool = new ItemStack(Items.DIAMOND_SWORD);
            tool.enchant(Enchantments.SHARPNESS, ThreadLocalRandom.current().nextInt(3, 6));
            if (ThreadLocalRandom.current().nextBoolean()) {
                tool.enchant(Enchantments.UNBREAKING, ThreadLocalRandom.current().nextInt(2, 4));
            }
        } else if (chance < 0.75) {
            tool = new ItemStack(Items.DIAMOND_AXE);
            tool.enchant(Enchantments.BLOCK_EFFICIENCY, ThreadLocalRandom.current().nextInt(3, 6));
            if (ThreadLocalRandom.current().nextBoolean()) {
                tool.enchant(Enchantments.UNBREAKING, ThreadLocalRandom.current().nextInt(2, 4));
            }
        } else {
            tool = new ItemStack(Items.BOW);
            tool.enchant(Enchantments.POWER_ARROWS, ThreadLocalRandom.current().nextInt(3, 6));
            if (ThreadLocalRandom.current().nextBoolean()) {
                tool.enchant(Enchantments.UNBREAKING, ThreadLocalRandom.current().nextInt(2, 4));
            }
        }
        
        if (!player.addItem(tool)) {
            player.drop(tool, false);
        }
    }
}
