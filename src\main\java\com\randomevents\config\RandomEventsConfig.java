package com.randomevents.config;

import net.minecraftforge.common.ForgeConfigSpec;
import net.minecraftforge.fml.ModLoadingContext;
import net.minecraftforge.fml.config.ModConfig;

public class RandomEventsConfig {
    public static final ForgeConfigSpec.Builder BUILDER = new ForgeConfigSpec.Builder();
    public static final ForgeConfigSpec SPEC;
    
    // General settings
    public static final ForgeConfigSpec.IntValue DEFAULT_EVENT_INTERVAL;
    public static final ForgeConfigSpec.BooleanValue AUTO_START_EVENTS;
    public static final ForgeConfigSpec.BooleanValue ENABLE_GOOD_EVENTS;
    public static final ForgeConfigSpec.BooleanValue ENABLE_BAD_EVENTS;
    
    // Event weights
    public static final ForgeConfigSpec.IntValue DIAMOND_RAIN_WEIGHT;
    public static final ForgeConfigSpec.IntValue GOLDEN_APPLE_WEIGHT;
    public static final ForgeConfigSpec.IntValue SPEED_BOOST_WEIGHT;
    public static final ForgeConfigSpec.IntValue LUCKY_FIND_WEIGHT;
    public static final ForgeConfigSpec.IntValue HEALTH_BOOST_WEIGHT;
    public static final ForgeConfigSpec.IntValue EXPERIENCE_SHOWER_WEIGHT;
    public static final ForgeConfigSpec.IntValue ENCHANTED_TOOL_WEIGHT;
    public static final ForgeConfigSpec.IntValue FOOD_FEAST_WEIGHT;
    public static final ForgeConfigSpec.IntValue FIRE_RESISTANCE_WEIGHT;
    public static final ForgeConfigSpec.IntValue NIGHT_VISION_WEIGHT;
    
    public static final ForgeConfigSpec.IntValue ZOMBIE_SWARM_WEIGHT;
    public static final ForgeConfigSpec.IntValue SKELETON_AMBUSH_WEIGHT;
    public static final ForgeConfigSpec.IntValue CREEPER_SURPRISE_WEIGHT;
    public static final ForgeConfigSpec.IntValue HUNGER_CURSE_WEIGHT;
    public static final ForgeConfigSpec.IntValue SLOWNESS_WEIGHT;
    public static final ForgeConfigSpec.IntValue BLINDNESS_WEIGHT;
    public static final ForgeConfigSpec.IntValue POISON_CLOUD_WEIGHT;
    public static final ForgeConfigSpec.IntValue WEAKNESS_WEIGHT;
    public static final ForgeConfigSpec.IntValue INVENTORY_SCRAMBLE_WEIGHT;
    public static final ForgeConfigSpec.IntValue LIGHTNING_STORM_WEIGHT;
    
    static {
        BUILDER.push("General Settings");
        
        DEFAULT_EVENT_INTERVAL = BUILDER
            .comment("Default interval between events in seconds")
            .defineInRange("defaultEventInterval", 300, 30, 3600);
            
        AUTO_START_EVENTS = BUILDER
            .comment("Whether to automatically start events when the server starts")
            .define("autoStartEvents", false);
            
        ENABLE_GOOD_EVENTS = BUILDER
            .comment("Enable good/beneficial events")
            .define("enableGoodEvents", true);
            
        ENABLE_BAD_EVENTS = BUILDER
            .comment("Enable bad/challenging events")
            .define("enableBadEvents", true);
        
        BUILDER.pop();
        
        BUILDER.push("Good Event Weights");
        
        DIAMOND_RAIN_WEIGHT = BUILDER
            .comment("Weight for Diamond Rain event (higher = more likely)")
            .defineInRange("diamondRainWeight", 5, 0, 100);
            
        GOLDEN_APPLE_WEIGHT = BUILDER
            .comment("Weight for Golden Apple Gift event")
            .defineInRange("goldenAppleWeight", 8, 0, 100);
            
        SPEED_BOOST_WEIGHT = BUILDER
            .comment("Weight for Speed Boost event")
            .defineInRange("speedBoostWeight", 10, 0, 100);
            
        LUCKY_FIND_WEIGHT = BUILDER
            .comment("Weight for Lucky Find event")
            .defineInRange("luckyFindWeight", 7, 0, 100);
            
        HEALTH_BOOST_WEIGHT = BUILDER
            .comment("Weight for Health Boost event")
            .defineInRange("healthBoostWeight", 9, 0, 100);
            
        EXPERIENCE_SHOWER_WEIGHT = BUILDER
            .comment("Weight for Experience Shower event")
            .defineInRange("experienceShowerWeight", 8, 0, 100);
            
        ENCHANTED_TOOL_WEIGHT = BUILDER
            .comment("Weight for Enchanted Tool event")
            .defineInRange("enchantedToolWeight", 6, 0, 100);
            
        FOOD_FEAST_WEIGHT = BUILDER
            .comment("Weight for Food Feast event")
            .defineInRange("foodFeastWeight", 9, 0, 100);
            
        FIRE_RESISTANCE_WEIGHT = BUILDER
            .comment("Weight for Fire Resistance event")
            .defineInRange("fireResistanceWeight", 7, 0, 100);
            
        NIGHT_VISION_WEIGHT = BUILDER
            .comment("Weight for Night Vision event")
            .defineInRange("nightVisionWeight", 8, 0, 100);
        
        BUILDER.pop();
        
        BUILDER.push("Bad Event Weights");
        
        ZOMBIE_SWARM_WEIGHT = BUILDER
            .comment("Weight for Zombie Swarm event")
            .defineInRange("zombieSwarmWeight", 8, 0, 100);
            
        SKELETON_AMBUSH_WEIGHT = BUILDER
            .comment("Weight for Skeleton Ambush event")
            .defineInRange("skeletonAmbushWeight", 7, 0, 100);
            
        CREEPER_SURPRISE_WEIGHT = BUILDER
            .comment("Weight for Creeper Surprise event")
            .defineInRange("creeperSurpriseWeight", 6, 0, 100);
            
        HUNGER_CURSE_WEIGHT = BUILDER
            .comment("Weight for Hunger Curse event")
            .defineInRange("hungerCurseWeight", 9, 0, 100);
            
        SLOWNESS_WEIGHT = BUILDER
            .comment("Weight for Slowness event")
            .defineInRange("slownessWeight", 10, 0, 100);
            
        BLINDNESS_WEIGHT = BUILDER
            .comment("Weight for Blindness event")
            .defineInRange("blindnessWeight", 8, 0, 100);
            
        POISON_CLOUD_WEIGHT = BUILDER
            .comment("Weight for Poison Cloud event")
            .defineInRange("poisonCloudWeight", 7, 0, 100);
            
        WEAKNESS_WEIGHT = BUILDER
            .comment("Weight for Weakness event")
            .defineInRange("weaknessWeight", 9, 0, 100);
            
        INVENTORY_SCRAMBLE_WEIGHT = BUILDER
            .comment("Weight for Inventory Scramble event")
            .defineInRange("inventoryScrambleWeight", 6, 0, 100);
            
        LIGHTNING_STORM_WEIGHT = BUILDER
            .comment("Weight for Lightning Storm event")
            .defineInRange("lightningStormWeight", 5, 0, 100);
        
        BUILDER.pop();
        
        SPEC = BUILDER.build();
    }
    
    public static void register() {
        ModLoadingContext.get().registerConfig(ModConfig.Type.COMMON, SPEC);
    }
}
