package com.randomevents.config;

import net.minecraftforge.common.ForgeConfigSpec;
import net.minecraftforge.fml.ModLoadingContext;
import net.minecraftforge.fml.config.ModConfig;

public class RandomEventsConfig {
    public static final ForgeConfigSpec.Builder BUILDER = new ForgeConfigSpec.Builder();
    public static final ForgeConfigSpec SPEC;
    
    // General settings
    public static final ForgeConfigSpec.IntValue DEFAULT_EVENT_INTERVAL;
    public static final ForgeConfigSpec.BooleanValue AUTO_START_EVENTS;
    public static final ForgeConfigSpec.BooleanValue ENABLE_GOOD_EVENTS;
    public static final ForgeConfigSpec.BooleanValue ENABLE_BAD_EVENTS;
    
    // Event weights
    public static final ForgeConfigSpec.IntValue CREEPER_SURPRISE_WEIGHT;
    public static final ForgeConfigSpec.IntValue STAY_TOGETHER_WEIGHT;
    public static final ForgeConfigSpec.IntValue GRAVITY_FLIP_WEIGHT;
    public static final ForgeConfigSpec.IntValue MIRROR_WORLD_WEIGHT;
    public static final ForgeConfigSpec.IntValue BLOCK_SWAP_WEIGHT;
    public static final ForgeConfigSpec.IntValue SHADOW_CLONE_WEIGHT;
    public static final ForgeConfigSpec.IntValue INVENTORY_ROULETTE_WEIGHT;
    public static final ForgeConfigSpec.IntValue PHANTOM_ZONE_WEIGHT;
    
    static {
        BUILDER.push("General Settings");
        
        DEFAULT_EVENT_INTERVAL = BUILDER
            .comment("Default interval between events in seconds")
            .defineInRange("defaultEventInterval", 300, 30, 3600);
            
        AUTO_START_EVENTS = BUILDER
            .comment("Whether to automatically start events when the server starts")
            .define("autoStartEvents", false);
            
        ENABLE_GOOD_EVENTS = BUILDER
            .comment("Enable good/beneficial events")
            .define("enableGoodEvents", true);
            
        ENABLE_BAD_EVENTS = BUILDER
            .comment("Enable bad/challenging events")
            .define("enableBadEvents", true);
        
        BUILDER.pop();
        
        BUILDER.push("Event Weights");

        CREEPER_SURPRISE_WEIGHT = BUILDER
            .comment("Weight for Creeper Surprise event")
            .defineInRange("creeperSurpriseWeight", 6, 0, 100);

        STAY_TOGETHER_WEIGHT = BUILDER
            .comment("Weight for Stay Together event")
            .defineInRange("stayTogetherWeight", 8, 0, 100);

        GRAVITY_FLIP_WEIGHT = BUILDER
            .comment("Weight for Gravity Flip event")
            .defineInRange("gravityFlipWeight", 6, 0, 100);

        MIRROR_WORLD_WEIGHT = BUILDER
            .comment("Weight for Mirror World event")
            .defineInRange("mirrorWorldWeight", 7, 0, 100);

        BLOCK_SWAP_WEIGHT = BUILDER
            .comment("Weight for Block Swap event")
            .defineInRange("blockSwapWeight", 8, 0, 100);

        SHADOW_CLONE_WEIGHT = BUILDER
            .comment("Weight for Shadow Clone event")
            .defineInRange("shadowCloneWeight", 7, 0, 100);

        INVENTORY_ROULETTE_WEIGHT = BUILDER
            .comment("Weight for Inventory Roulette event")
            .defineInRange("inventoryRouletteWeight", 6, 0, 100);

        PHANTOM_ZONE_WEIGHT = BUILDER
            .comment("Weight for Phantom Zone event")
            .defineInRange("phantomZoneWeight", 5, 0, 100);
        
        BUILDER.pop();
        
        SPEC = BUILDER.build();
    }
    
    public static void register() {
        ModLoadingContext.get().registerConfig(ModConfig.Type.COMMON, SPEC);
    }
}
