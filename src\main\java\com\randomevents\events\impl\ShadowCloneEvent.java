package com.randomevents.events.impl;

import com.randomevents.events.RandomEvent;
import net.minecraft.entity.EntityType;
import net.minecraft.entity.monster.ZombieEntity;
import net.minecraft.entity.player.ServerPlayerEntity;
import net.minecraft.inventory.EquipmentSlotType;
import net.minecraft.item.ItemStack;
import net.minecraft.potion.EffectInstance;
import net.minecraft.potion.Effects;
import net.minecraft.util.text.StringTextComponent;
import net.minecraft.util.text.TextFormatting;
import net.minecraft.world.server.ServerWorld;
import net.minecraftforge.common.MinecraftForge;
import net.minecraftforge.event.TickEvent;
import net.minecraftforge.eventbus.api.SubscribeEvent;

import java.util.*;
import java.util.concurrent.ThreadLocalRandom;

public class ShadowCloneEvent extends RandomEvent {
    private static final Map<UUID, ShadowCloneInstance> activeInstances = new HashMap<>();
    
    public ShadowCloneEvent() {
        super("shadow_clone", "Shadow Doppelgangers", "Dark copies of yourself appear to confuse and hunt you!", EventType.BAD, 7);
    }
    
    @Override
    public void execute(ServerPlayerEntity player, ServerWorld world) {
        List<ServerPlayerEntity> allPlayers = world.getServer().getPlayerList().getPlayers();
        
        for (ServerPlayerEntity p : allPlayers) {
            // Show title at top of screen
            p.connection.send(new net.minecraft.network.play.server.STitlePacket(
                net.minecraft.network.play.server.STitlePacket.Type.TITLE,
                new StringTextComponent("Shadow Doppelgangers").withStyle(TextFormatting.DARK_GRAY)
            ));

            ShadowCloneInstance instance = new ShadowCloneInstance(p, world);
            activeInstances.put(p.getUUID(), instance);
        }
        
        // Register the tick handler if not already registered
        if (!activeInstances.isEmpty()) {
            MinecraftForge.EVENT_BUS.register(new ShadowCloneTickHandler());
        }
    }
    
    public static class ShadowCloneInstance {
        private final UUID playerUUID;
        private final ServerWorld world;
        private int ticksRemaining;
        private final List<ZombieEntity> shadowClones;
        private int nextSpawnTick;
        
        public ShadowCloneInstance(ServerPlayerEntity player, ServerWorld world) {
            this.playerUUID = player.getUUID();
            this.world = world;
            this.ticksRemaining = 600; // 30 seconds
            this.shadowClones = new ArrayList<>();
            this.nextSpawnTick = 60; // Spawn first clone after 3 seconds
            
            spawnInitialClone(player);
        }
        
        private void spawnInitialClone(ServerPlayerEntity player) {
            // Spawn first shadow clone immediately
            createShadowClone(player);
        }
        
        private void createShadowClone(ServerPlayerEntity player) {
            if (shadowClones.size() >= 3) return; // Max 3 clones
            
            // Spawn zombie as shadow clone
            ZombieEntity clone = new ZombieEntity(EntityType.ZOMBIE, world);
            
            // Position clone near player
            double angle = ThreadLocalRandom.current().nextDouble() * 2 * Math.PI;
            double distance = 8 + ThreadLocalRandom.current().nextDouble() * 8; // 8-16 blocks away
            double x = player.getX() + Math.cos(angle) * distance;
            double z = player.getZ() + Math.sin(angle) * distance;
            double y = player.getY();
            
            clone.setPos(x, y, z);
            
            // Copy player's equipment to make it look like them
            copyPlayerEquipment(player, clone);
            
            // Make it stronger and faster
            clone.getAttribute(net.minecraft.entity.ai.attributes.Attributes.MAX_HEALTH).setBaseValue(40.0);
            clone.setHealth(40.0f);
            clone.getAttribute(net.minecraft.entity.ai.attributes.Attributes.MOVEMENT_SPEED).setBaseValue(0.35);
            clone.getAttribute(net.minecraft.entity.ai.attributes.Attributes.ATTACK_DAMAGE).setBaseValue(6.0);
            
            // Add effects to make it look shadowy
            clone.addEffect(new EffectInstance(Effects.MOVEMENT_SPEED, Integer.MAX_VALUE, 1, false, false));
            clone.addEffect(new EffectInstance(Effects.DAMAGE_RESISTANCE, Integer.MAX_VALUE, 0, false, false));
            
            // Set target to player
            clone.setTarget(player);
            
            // Add custom name
            clone.setCustomName(new StringTextComponent("Shadow " + player.getName().getString()).withStyle(TextFormatting.DARK_GRAY));
            clone.setCustomNameVisible(true);
            
            world.addFreshEntity(clone);
            shadowClones.add(clone);
        }
        
        private void copyPlayerEquipment(ServerPlayerEntity player, ZombieEntity clone) {
            // Copy armor
            for (EquipmentSlotType slot : EquipmentSlotType.values()) {
                if (slot.getType() == EquipmentSlotType.Group.ARMOR || slot == EquipmentSlotType.MAINHAND) {
                    ItemStack playerItem = player.getItemBySlot(slot);
                    if (!playerItem.isEmpty()) {
                        clone.setItemSlot(slot, playerItem.copy());
                    }
                }
            }
        }
        
        public boolean tick() {
            ticksRemaining--;
            nextSpawnTick--;
            
            ServerPlayerEntity player = world.getServer().getPlayerList().getPlayer(playerUUID);
            if (player == null || !player.isAlive()) {
                cleanup();
                return false;
            }
            
            if (ticksRemaining <= 0) {
                cleanup();
                return false;
            }
            
            // Spawn additional clones periodically
            if (nextSpawnTick <= 0 && shadowClones.size() < 3) {
                createShadowClone(player);
                nextSpawnTick = 300 + ThreadLocalRandom.current().nextInt(200); // 15-25 seconds
            }
            
            // Remove dead clones
            shadowClones.removeIf(clone -> !clone.isAlive());
            
            // Update clone targets
            for (ZombieEntity clone : shadowClones) {
                if (clone.getTarget() != player) {
                    clone.setTarget(player);
                }
            }
            
            return true;
        }
        
        private void cleanup() {
            for (ZombieEntity clone : shadowClones) {
                if (clone.isAlive()) {
                    clone.remove();
                }
            }
            shadowClones.clear();
        }
    }
    
    public static class ShadowCloneTickHandler {
        @SubscribeEvent
        public void onServerTick(TickEvent.ServerTickEvent event) {
            if (event.phase != TickEvent.Phase.END) return;
            
            Iterator<Map.Entry<UUID, ShadowCloneInstance>> iterator = activeInstances.entrySet().iterator();
            
            while (iterator.hasNext()) {
                Map.Entry<UUID, ShadowCloneInstance> entry = iterator.next();
                
                if (!entry.getValue().tick()) {
                    iterator.remove();
                }
            }
            
            // Unregister if no active instances
            if (activeInstances.isEmpty()) {
                MinecraftForge.EVENT_BUS.unregister(this);
            }
        }
    }
}
