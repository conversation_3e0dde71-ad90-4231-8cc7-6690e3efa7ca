package com.randomevents.events.impl;

import com.randomevents.events.RandomEvent;
import net.minecraft.entity.player.ServerPlayerEntity;
import net.minecraft.potion.EffectInstance;
import net.minecraft.potion.Effects;
import net.minecraft.world.server.ServerWorld;

public class SpeedBoostEvent extends RandomEvent {
    
    public SpeedBoostEvent() {
        super("speed_boost", "Speed Boost", "Gain incredible speed for 2 minutes!", EventType.GOOD, 10);
    }
    
    @Override
    public void execute(ServerPlayerEntity player, ServerWorld world) {
        // Speed II for 2 minutes (2400 ticks)
        EffectInstance speedEffect = new EffectInstance(Effects.MOVEMENT_SPEED, 2400, 1);
        player.addEffect(speedEffect);
    }
}
