package com.randomevents.client.gui;

import com.mojang.blaze3d.matrix.MatrixStack;
import com.randomevents.RandomEventsMod;
import com.randomevents.events.RandomEvent;
import net.minecraft.client.gui.screen.Screen;
import net.minecraft.client.gui.widget.button.Button;
import net.minecraft.client.gui.widget.TextFieldWidget;
import net.minecraft.util.text.StringTextComponent;
import net.minecraft.util.text.TextFormatting;

import java.util.List;

public class RandomEventsScreen extends Screen {
    private static final int BUTTON_WIDTH = 200;
    private static final int BUTTON_HEIGHT = 20;
    private static final int SPACING = 25;
    
    private Button startPauseButton;
    private Button triggerNowButton;
    private Button prevEventButton;
    private Button nextEventButton;
    private Button manualModeButton;
    private TextFieldWidget timerField;
    
    private int selectedEventIndex = -1;
    
    public RandomEventsScreen() {
        super(new StringTextComponent("Random Events Control"));
    }
    
    @Override
    protected void init() {
        super.init();
        
        int centerX = this.width / 2;
        int startY = this.height / 2 - 100;
        
        // Start/Pause button
        startPauseButton = addButton(new Button(
            centerX - BUTTON_WIDTH / 2, startY,
            BUTTON_WIDTH, BUTTON_HEIGHT,
            getStartPauseText(),
            button -> toggleStartPause()
        ));
        
        // Trigger Now button
        triggerNowButton = addButton(new Button(
            centerX - BUTTON_WIDTH / 2, startY + SPACING,
            BUTTON_WIDTH, BUTTON_HEIGHT,
            new StringTextComponent("Trigger Event Now"),
            button -> triggerEventNow()
        ));
        
        // Timer interval field
        timerField = new TextFieldWidget(
            this.font,
            centerX - 50, startY + SPACING * 2,
            100, BUTTON_HEIGHT,
            new StringTextComponent("Timer")
        );
        timerField.setValue(String.valueOf(getEventInterval()));
        timerField.setMaxLength(4);
        addWidget(timerField);
        
        // Timer set button
        addButton(new Button(
            centerX + 60, startY + SPACING * 2,
            80, BUTTON_HEIGHT,
            new StringTextComponent("Set Timer"),
            button -> setTimer()
        ));
        
        // Manual mode toggle
        manualModeButton = addButton(new Button(
            centerX - BUTTON_WIDTH / 2, startY + SPACING * 3,
            BUTTON_WIDTH, BUTTON_HEIGHT,
            getManualModeText(),
            button -> toggleManualMode()
        ));
        
        // Event navigation buttons
        prevEventButton = addButton(new Button(
            centerX - BUTTON_WIDTH / 2 - 30, startY + SPACING * 4,
            25, BUTTON_HEIGHT,
            new StringTextComponent("<"),
            button -> previousEvent()
        ));
        
        nextEventButton = addButton(new Button(
            centerX + BUTTON_WIDTH / 2 + 5, startY + SPACING * 4,
            25, BUTTON_HEIGHT,
            new StringTextComponent(">"),
            button -> nextEvent()
        ));
        
        updateButtonStates();
    }
    
    private StringTextComponent getStartPauseText() {
        return new StringTextComponent(isRunning() ? "Pause Events" : "Start Events");
    }
    
    private StringTextComponent getManualModeText() {
        return new StringTextComponent(isManualMode() ? "Manual Mode: ON" : "Manual Mode: OFF");
    }
    
    private void toggleStartPause() {
        if (RandomEventsMod.eventManager != null) {
            if (isRunning()) {
                RandomEventsMod.eventManager.pause();
            } else {
                RandomEventsMod.eventManager.start();
            }
            startPauseButton.setMessage(getStartPauseText());
        }
    }
    
    private void triggerEventNow() {
        if (RandomEventsMod.eventManager != null) {
            RandomEventsMod.eventManager.triggerEventNow();
        }
    }
    
    private void setTimer() {
        try {
            int seconds = Integer.parseInt(timerField.getValue());
            if (seconds > 0 && seconds <= 3600) { // Max 1 hour
                if (RandomEventsMod.eventManager != null) {
                    RandomEventsMod.eventManager.setEventInterval(seconds);
                }
            }
        } catch (NumberFormatException e) {
            timerField.setValue(String.valueOf(getEventInterval()));
        }
    }
    
    private void toggleManualMode() {
        if (RandomEventsMod.eventManager != null) {
            if (isManualMode()) {
                RandomEventsMod.eventManager.setManualEvent(-1);
                selectedEventIndex = -1;
            } else {
                selectedEventIndex = 0;
                RandomEventsMod.eventManager.setManualEvent(selectedEventIndex);
            }
            manualModeButton.setMessage(getManualModeText());
            updateButtonStates();
        }
    }
    
    private void previousEvent() {
        List<RandomEvent> events = getEvents();
        if (!events.isEmpty() && selectedEventIndex > 0) {
            selectedEventIndex--;
            if (RandomEventsMod.eventManager != null) {
                RandomEventsMod.eventManager.setManualEvent(selectedEventIndex);
            }
        }
    }
    
    private void nextEvent() {
        List<RandomEvent> events = getEvents();
        if (!events.isEmpty() && selectedEventIndex < events.size() - 1) {
            selectedEventIndex++;
            if (RandomEventsMod.eventManager != null) {
                RandomEventsMod.eventManager.setManualEvent(selectedEventIndex);
            }
        }
    }
    
    private void updateButtonStates() {
        boolean manualMode = isManualMode();
        prevEventButton.active = manualMode;
        nextEventButton.active = manualMode;
    }
    
    @Override
    public void render(MatrixStack matrixStack, int mouseX, int mouseY, float partialTicks) {
        this.renderBackground(matrixStack);
        super.render(matrixStack, mouseX, mouseY, partialTicks);
        
        // Draw title
        drawCenteredString(matrixStack, this.font, this.title, this.width / 2, 20, 0xFFFFFF);
        
        // Draw status information
        int centerX = this.width / 2;
        int statusY = this.height / 2 + 50;
        
        // Current status
        String status = isRunning() ? "Running" : "Paused";
        TextFormatting statusColor = isRunning() ? TextFormatting.GREEN : TextFormatting.RED;
        drawCenteredString(matrixStack, this.font, 
            new StringTextComponent("Status: ").append(new StringTextComponent(status).withStyle(statusColor)),
            centerX, statusY, 0xFFFFFF);
        
        // Time until next event
        if (isRunning()) {
            int timeLeft = getTimeUntilNextEvent();
            if (timeLeft >= 0) {
                drawCenteredString(matrixStack, this.font,
                    new StringTextComponent("Next event in: " + timeLeft + " seconds"),
                    centerX, statusY + 15, 0xCCCCCC);
            }
        }
        
        // Current event info
        RandomEvent currentEvent = getCurrentEvent();
        if (currentEvent != null) {
            drawCenteredString(matrixStack, this.font,
                new StringTextComponent("Last event: ").append(currentEvent.getFormattedName()),
                centerX, statusY + 30, 0xFFFFFF);
        }
        
        // Manual mode event selection
        if (isManualMode()) {
            List<RandomEvent> events = getEvents();
            if (!events.isEmpty() && selectedEventIndex >= 0 && selectedEventIndex < events.size()) {
                RandomEvent selectedEvent = events.get(selectedEventIndex);
                drawCenteredString(matrixStack, this.font,
                    new StringTextComponent("Selected: ").append(selectedEvent.getFormattedName()),
                    centerX, statusY + 45, 0xFFFFFF);
                
                drawCenteredString(matrixStack, this.font,
                    selectedEvent.getFormattedDescription(),
                    centerX, statusY + 60, 0xFFFFFF);
            }
        }
        
        // Timer field label
        drawString(matrixStack, this.font, "Timer (seconds):", centerX - 50, statusY - 85, 0xFFFFFF);
        timerField.render(matrixStack, mouseX, mouseY, partialTicks);
    }
    
    @Override
    public boolean isPauseScreen() {
        return false;
    }
    
    // Helper methods to get data from the event manager
    private boolean isRunning() {
        return RandomEventsMod.eventManager != null && RandomEventsMod.eventManager.isRunning();
    }
    
    private boolean isManualMode() {
        return RandomEventsMod.eventManager != null && RandomEventsMod.eventManager.isManualEventMode();
    }
    
    private int getEventInterval() {
        return RandomEventsMod.eventManager != null ? RandomEventsMod.eventManager.getEventInterval() : 300;
    }
    
    private int getTimeUntilNextEvent() {
        return RandomEventsMod.eventManager != null ? RandomEventsMod.eventManager.getTimeUntilNextEvent() : -1;
    }
    
    private RandomEvent getCurrentEvent() {
        return RandomEventsMod.eventManager != null ? RandomEventsMod.eventManager.getCurrentEvent() : null;
    }
    
    private List<RandomEvent> getEvents() {
        return RandomEventsMod.eventManager != null ? RandomEventsMod.eventManager.getEvents() : java.util.Collections.emptyList();
    }
}
