package com.randomevents.events.impl;

import com.randomevents.events.RandomEvent;
import net.minecraft.entity.player.ServerPlayerEntity;
import net.minecraft.potion.EffectInstance;
import net.minecraft.potion.Effects;
import net.minecraft.world.server.ServerWorld;

public class PoisonCloudEvent extends RandomEvent {
    
    public PoisonCloudEvent() {
        super("poison_cloud", "Poison Cloud", "Toxic fumes poison you!", EventType.BAD, 7);
    }
    
    @Override
    public void execute(ServerPlayerEntity player, ServerWorld world) {
        // Poison II for 45 seconds (900 ticks)
        EffectInstance poison = new EffectInstance(Effects.POISON, 900, 1);
        player.addEffect(poison);
        
        // Also add nausea for disorientation
        EffectInstance nausea = new EffectInstance(Effects.CONFUSION, 600, 0);
        player.addEffect(nausea);
    }
}
