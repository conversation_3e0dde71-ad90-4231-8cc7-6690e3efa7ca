package com.randomevents.commands;

import com.mojang.brigadier.CommandDispatcher;
import com.mojang.brigadier.arguments.IntegerArgumentType;
import com.mojang.brigadier.arguments.StringArgumentType;
import com.mojang.brigadier.context.CommandContext;
import com.mojang.brigadier.exceptions.CommandSyntaxException;
import com.randomevents.RandomEventsMod;
import com.randomevents.events.RandomEvent;
import net.minecraft.command.CommandSource;
import net.minecraft.command.Commands;
import net.minecraft.util.text.StringTextComponent;
import net.minecraft.util.text.TextFormatting;

import java.util.List;

public class RandomEventsCommand {
    
    public static void register(CommandDispatcher<CommandSource> dispatcher) {
        dispatcher.register(Commands.literal("randomevents")
            .requires(source -> source.hasPermission(2)) // Requires OP level 2
            .then(Commands.literal("start")
                .executes(RandomEventsCommand::startEvents))
            .then(Commands.literal("pause")
                .executes(RandomEventsCommand::pauseEvents))
            .then(Commands.literal("trigger")
                .executes(RandomEventsCommand::triggerEvent))
            .then(Commands.literal("timer")
                .then(Commands.argument("seconds", IntegerArgumentType.integer(1, 3600))
                    .executes(RandomEventsCommand::setTimer)))
            .then(Commands.literal("manual")
                .then(Commands.argument("eventId", StringArgumentType.string())
                    .executes(RandomEventsCommand::setManualEvent)))
            .then(Commands.literal("auto")
                .executes(RandomEventsCommand::setAutoMode))
            .then(Commands.literal("list")
                .executes(RandomEventsCommand::listEvents))
            .then(Commands.literal("status")
                .executes(RandomEventsCommand::showStatus))
        );
    }
    
    private static int startEvents(CommandContext<CommandSource> context) {
        if (RandomEventsMod.eventManager != null) {
            RandomEventsMod.eventManager.start();
            context.getSource().sendSuccess(
                new StringTextComponent("Random events started").withStyle(TextFormatting.GREEN), 
                true
            );
        } else {
            context.getSource().sendFailure(
                new StringTextComponent("Event manager not available").withStyle(TextFormatting.RED)
            );
        }
        return 1;
    }
    
    private static int pauseEvents(CommandContext<CommandSource> context) {
        if (RandomEventsMod.eventManager != null) {
            RandomEventsMod.eventManager.pause();
            context.getSource().sendSuccess(
                new StringTextComponent("Random events paused").withStyle(TextFormatting.YELLOW), 
                true
            );
        } else {
            context.getSource().sendFailure(
                new StringTextComponent("Event manager not available").withStyle(TextFormatting.RED)
            );
        }
        return 1;
    }
    
    private static int triggerEvent(CommandContext<CommandSource> context) {
        if (RandomEventsMod.eventManager != null) {
            RandomEventsMod.eventManager.triggerEventNow();
            context.getSource().sendSuccess(
                new StringTextComponent("Triggered random event").withStyle(TextFormatting.GREEN), 
                true
            );
        } else {
            context.getSource().sendFailure(
                new StringTextComponent("Event manager not available").withStyle(TextFormatting.RED)
            );
        }
        return 1;
    }
    
    private static int setTimer(CommandContext<CommandSource> context) {
        int seconds = IntegerArgumentType.getInteger(context, "seconds");
        if (RandomEventsMod.eventManager != null) {
            RandomEventsMod.eventManager.setEventInterval(seconds);
            context.getSource().sendSuccess(
                new StringTextComponent("Set event timer to " + seconds + " seconds").withStyle(TextFormatting.GREEN), 
                true
            );
        } else {
            context.getSource().sendFailure(
                new StringTextComponent("Event manager not available").withStyle(TextFormatting.RED)
            );
        }
        return 1;
    }
    
    private static int setManualEvent(CommandContext<CommandSource> context) {
        String eventId = StringArgumentType.getString(context, "eventId");
        if (RandomEventsMod.eventManager != null) {
            List<RandomEvent> events = RandomEventsMod.eventManager.getEvents();
            for (int i = 0; i < events.size(); i++) {
                if (events.get(i).getId().equals(eventId)) {
                    RandomEventsMod.eventManager.setManualEvent(i);
                    context.getSource().sendSuccess(
                        new StringTextComponent("Set manual event to: " + events.get(i).getName()).withStyle(TextFormatting.GREEN), 
                        true
                    );
                    return 1;
                }
            }
            context.getSource().sendFailure(
                new StringTextComponent("Event not found: " + eventId).withStyle(TextFormatting.RED)
            );
        } else {
            context.getSource().sendFailure(
                new StringTextComponent("Event manager not available").withStyle(TextFormatting.RED)
            );
        }
        return 1;
    }
    
    private static int setAutoMode(CommandContext<CommandSource> context) {
        if (RandomEventsMod.eventManager != null) {
            RandomEventsMod.eventManager.setManualEvent(-1);
            context.getSource().sendSuccess(
                new StringTextComponent("Set to automatic mode").withStyle(TextFormatting.GREEN), 
                true
            );
        } else {
            context.getSource().sendFailure(
                new StringTextComponent("Event manager not available").withStyle(TextFormatting.RED)
            );
        }
        return 1;
    }
    
    private static int listEvents(CommandContext<CommandSource> context) {
        if (RandomEventsMod.eventManager != null) {
            List<RandomEvent> events = RandomEventsMod.eventManager.getEvents();
            context.getSource().sendSuccess(
                new StringTextComponent("Available Events:").withStyle(TextFormatting.YELLOW), 
                false
            );
            
            for (RandomEvent event : events) {
                context.getSource().sendSuccess(
                    new StringTextComponent("- " + event.getId() + ": ")
                        .append(event.getFormattedName())
                        .append(new StringTextComponent(" - " + event.getDescription()).withStyle(TextFormatting.GRAY)), 
                    false
                );
            }
        } else {
            context.getSource().sendFailure(
                new StringTextComponent("Event manager not available").withStyle(TextFormatting.RED)
            );
        }
        return 1;
    }
    
    private static int showStatus(CommandContext<CommandSource> context) {
        if (RandomEventsMod.eventManager != null) {
            boolean running = RandomEventsMod.eventManager.isRunning();
            int interval = RandomEventsMod.eventManager.getEventInterval();
            int timeLeft = RandomEventsMod.eventManager.getTimeUntilNextEvent();
            boolean manual = RandomEventsMod.eventManager.isManualEventMode();
            
            context.getSource().sendSuccess(
                new StringTextComponent("Random Events Status:").withStyle(TextFormatting.YELLOW), 
                false
            );
            
            context.getSource().sendSuccess(
                new StringTextComponent("Running: " + (running ? "Yes" : "No"))
                    .withStyle(running ? TextFormatting.GREEN : TextFormatting.RED), 
                false
            );
            
            context.getSource().sendSuccess(
                new StringTextComponent("Interval: " + interval + " seconds").withStyle(TextFormatting.WHITE), 
                false
            );
            
            if (running && timeLeft >= 0) {
                context.getSource().sendSuccess(
                    new StringTextComponent("Next event in: " + timeLeft + " seconds").withStyle(TextFormatting.WHITE), 
                    false
                );
            }
            
            context.getSource().sendSuccess(
                new StringTextComponent("Mode: " + (manual ? "Manual" : "Automatic")).withStyle(TextFormatting.WHITE), 
                false
            );
            
            RandomEvent currentEvent = RandomEventsMod.eventManager.getCurrentEvent();
            if (currentEvent != null) {
                context.getSource().sendSuccess(
                    new StringTextComponent("Last event: ").append(currentEvent.getFormattedName()), 
                    false
                );
            }
        } else {
            context.getSource().sendFailure(
                new StringTextComponent("Event manager not available").withStyle(TextFormatting.RED)
            );
        }
        return 1;
    }
}
