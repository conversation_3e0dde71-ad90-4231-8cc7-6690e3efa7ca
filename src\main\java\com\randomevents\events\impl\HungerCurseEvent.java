package com.randomevents.events.impl;

import com.randomevents.events.RandomEvent;
import net.minecraft.entity.player.ServerPlayerEntity;
import net.minecraft.potion.EffectInstance;
import net.minecraft.potion.Effects;
import net.minecraft.world.server.ServerWorld;

public class HungerCurseEvent extends RandomEvent {
    
    public HungerCurseEvent() {
        super("hunger_curse", "Hunger Curse", "You feel extremely hungry and weak!", EventType.BAD, 9);
    }
    
    @Override
    public void execute(ServerPlayerEntity player, ServerWorld world) {
        // Hunger effect for 3 minutes (3600 ticks)
        EffectInstance hunger = new EffectInstance(Effects.HUNGER, 3600, 1);
        player.addEffect(hunger);
        
        // Also drain current food level
        player.getFoodData().setFoodLevel(Math.max(1, player.getFoodData().getFoodLevel() - 15));
        player.getFoodData().setSaturation(0.0f);
    }
}
