package com.randomevents.events.impl;

import com.randomevents.events.RandomEvent;
import net.minecraft.entity.EntityType;
import net.minecraft.entity.monster.SkeletonEntity;
import net.minecraft.entity.player.ServerPlayerEntity;
import net.minecraft.item.ItemStack;
import net.minecraft.item.Items;
import net.minecraft.util.math.BlockPos;
import net.minecraft.world.server.ServerWorld;

import java.util.concurrent.ThreadLocalRandom;

public class SkeletonAmbushEvent extends RandomEvent {
    
    public SkeletonAmbushEvent() {
        super("skeleton_ambush", "Skeleton Ambush", "Skeletons with bows surround you!", EventType.BAD, 7);
    }
    
    @Override
    public void execute(ServerPlayerEntity player, ServerWorld world) {
        BlockPos playerPos = player.blockPosition();
        int skeletonCount = ThreadLocalRandom.current().nextInt(4, 8); // 4-7 skeletons
        
        for (int i = 0; i < skeletonCount; i++) {
            // Spawn skeletons at a distance
            double angle = (2 * Math.PI * i) / skeletonCount;
            double radius = ThreadLocalRandom.current().nextDouble(12, 20);
            
            double x = playerPos.getX() + Math.cos(angle) * radius;
            double z = playerPos.getZ() + Math.sin(angle) * radius;
            double y = world.getHeightmapPos(net.minecraft.world.Heightmap.Type.MOTION_BLOCKING_NO_LEAVES, 
                                           new BlockPos(x, 0, z)).getY();
            
            SkeletonEntity skeleton = new SkeletonEntity(EntityType.SKELETON, world);
            skeleton.setPos(x, y, z);
            skeleton.setTarget(player);
            
            // Give them bows and arrows
            skeleton.setItemSlot(net.minecraft.inventory.EquipmentSlotType.MAINHAND, new ItemStack(Items.BOW));
            
            // Some skeletons get better equipment
            if (ThreadLocalRandom.current().nextDouble() < 0.4) { // 40% chance
                ItemStack enchantedBow = new ItemStack(Items.BOW);
                enchantedBow.enchant(net.minecraft.enchantment.Enchantments.POWER_ARROWS, 
                                   ThreadLocalRandom.current().nextInt(2, 4));
                skeleton.setItemSlot(net.minecraft.inventory.EquipmentSlotType.MAINHAND, enchantedBow);
            }
            
            world.addFreshEntity(skeleton);
        }
    }
}
