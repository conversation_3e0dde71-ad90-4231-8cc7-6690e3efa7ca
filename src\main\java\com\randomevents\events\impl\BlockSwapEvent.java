package com.randomevents.events.impl;

import com.randomevents.events.RandomEvent;
import net.minecraft.block.BlockState;
import net.minecraft.block.Blocks;
import net.minecraft.entity.player.ServerPlayerEntity;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.text.StringTextComponent;
import net.minecraft.util.text.TextFormatting;
import net.minecraft.world.server.ServerWorld;
import net.minecraftforge.common.MinecraftForge;
import net.minecraftforge.event.TickEvent;
import net.minecraftforge.eventbus.api.SubscribeEvent;

import java.util.*;
import java.util.concurrent.ThreadLocalRandom;

public class BlockSwapEvent extends RandomEvent {
    private static final Map<UUID, BlockSwapInstance> activeInstances = new HashMap<>();
    
    public BlockSwapEvent() {
        super("block_swap", "Reality Shift", "The world around you is constantly changing!", EventType.BAD, 8);
    }
    
    @Override
    public void execute(ServerPlayerEntity player, ServerWorld world) {
        List<ServerPlayerEntity> allPlayers = world.getServer().getPlayerList().getPlayers();
        
        for (ServerPlayerEntity p : allPlayers) {
            // Show title at top of screen
            p.connection.send(new net.minecraft.network.play.server.STitlePacket(
                net.minecraft.network.play.server.STitlePacket.Type.TITLE,
                new StringTextComponent("Reality Shift").withStyle(TextFormatting.AQUA)
            ));

            BlockSwapInstance instance = new BlockSwapInstance(p, world);
            activeInstances.put(p.getUUID(), instance);
        }
        
        // Register the tick handler if not already registered
        if (!activeInstances.isEmpty()) {
            MinecraftForge.EVENT_BUS.register(new BlockSwapTickHandler());
        }
    }
    
    public static class BlockSwapInstance {
        private final UUID playerUUID;
        private final ServerWorld world;
        private int ticksRemaining;
        private int nextSwapTick;
        private final Set<BlockPos> swappedPositions;
        
        public BlockSwapInstance(ServerPlayerEntity player, ServerWorld world) {
            this.playerUUID = player.getUUID();
            this.world = world;
            this.ticksRemaining = 600; // 30 seconds
            this.nextSwapTick = 20; // First swap after 1 second
            this.swappedPositions = new HashSet<>();
        }
        
        public boolean tick() {
            ticksRemaining--;
            nextSwapTick--;
            
            ServerPlayerEntity player = world.getServer().getPlayerList().getPlayer(playerUUID);
            if (player == null || !player.isAlive()) {
                return false;
            }
            
            if (ticksRemaining <= 0) {
                return false;
            }
            
            if (nextSwapTick <= 0) {
                performBlockSwaps(player);
                nextSwapTick = 30 + ThreadLocalRandom.current().nextInt(40); // 1.5-3.5 seconds
            }
            
            return true;
        }
        
        private void performBlockSwaps(ServerPlayerEntity player) {
            BlockPos playerPos = player.blockPosition();
            List<BlockPos> nearbyPositions = new ArrayList<>();
            
            // Find blocks in a 10x10x10 area around player
            for (int x = -5; x <= 5; x++) {
                for (int y = -3; y <= 6; y++) {
                    for (int z = -5; z <= 5; z++) {
                        BlockPos pos = playerPos.offset(x, y, z);
                        BlockState state = world.getBlockState(pos);
                        
                        // Don't swap air, bedrock, or blocks too close to player
                        if (!state.isAir() && 
                            state.getBlock() != Blocks.BEDROCK && 
                            state.getBlock() != Blocks.BARRIER &&
                            Math.abs(x) + Math.abs(y) + Math.abs(z) > 2) {
                            nearbyPositions.add(pos);
                        }
                    }
                }
            }
            
            if (nearbyPositions.size() < 4) return;
            
            // Perform 3-5 random swaps
            int swapCount = ThreadLocalRandom.current().nextInt(3, 6);
            for (int i = 0; i < swapCount; i++) {
                if (nearbyPositions.size() < 2) break;
                
                BlockPos pos1 = nearbyPositions.get(ThreadLocalRandom.current().nextInt(nearbyPositions.size()));
                BlockPos pos2 = nearbyPositions.get(ThreadLocalRandom.current().nextInt(nearbyPositions.size()));
                
                if (!pos1.equals(pos2)) {
                    swapBlocks(pos1, pos2);
                    swappedPositions.add(pos1);
                    swappedPositions.add(pos2);
                }
            }
            
            // No feedback messages
        }
        
        private void swapBlocks(BlockPos pos1, BlockPos pos2) {
            BlockState state1 = world.getBlockState(pos1);
            BlockState state2 = world.getBlockState(pos2);
            
            // Perform the swap
            world.setBlock(pos1, state2, 3);
            world.setBlock(pos2, state1, 3);
        }
    }
    
    public static class BlockSwapTickHandler {
        @SubscribeEvent
        public void onServerTick(TickEvent.ServerTickEvent event) {
            if (event.phase != TickEvent.Phase.END) return;
            
            Iterator<Map.Entry<UUID, BlockSwapInstance>> iterator = activeInstances.entrySet().iterator();
            
            while (iterator.hasNext()) {
                Map.Entry<UUID, BlockSwapInstance> entry = iterator.next();
                
                if (!entry.getValue().tick()) {
                    iterator.remove();
                }
            }
            
            // Unregister if no active instances
            if (activeInstances.isEmpty()) {
                MinecraftForge.EVENT_BUS.unregister(this);
            }
        }
    }
}
