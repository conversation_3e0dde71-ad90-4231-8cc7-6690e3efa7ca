package com.randomevents.events.impl;

import com.randomevents.events.RandomEvent;
import net.minecraft.entity.player.ServerPlayerEntity;
import net.minecraft.item.ItemStack;
import net.minecraft.world.server.ServerWorld;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.ThreadLocalRandom;

public class InventoryScrambleEvent extends RandomEvent {
    
    public InventoryScrambleEvent() {
        super("inventory_scramble", "Inventory Scramble", "Your inventory gets completely shuffled!", EventType.BAD, 6);
    }
    
    @Override
    public void execute(ServerPlayerEntity player, ServerWorld world) {
        // Get all items from inventory (excluding armor and offhand)
        List<ItemStack> items = new ArrayList<>();
        
        for (int i = 0; i < player.inventory.getContainerSize(); i++) {
            ItemStack stack = player.inventory.getItem(i);
            if (!stack.isEmpty()) {
                items.add(stack.copy());
                player.inventory.setItem(i, ItemStack.EMPTY);
            }
        }
        
        // Shuffle the items
        Collections.shuffle(items, ThreadLocalRandom.current());
        
        // Put them back in random slots
        int itemIndex = 0;
        for (int i = 0; i < player.inventory.getContainerSize() && itemIndex < items.size(); i++) {
            if (player.inventory.getItem(i).isEmpty()) {
                player.inventory.setItem(i, items.get(itemIndex));
                itemIndex++;
            }
        }
        
        // Drop any remaining items that couldn't fit
        while (itemIndex < items.size()) {
            player.drop(items.get(itemIndex), false);
            itemIndex++;
        }
    }
}
