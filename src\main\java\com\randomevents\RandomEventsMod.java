package com.randomevents;

import net.minecraft.client.settings.KeyBinding;
import net.minecraft.client.util.InputMappings;
import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.api.distmarker.OnlyIn;
import net.minecraftforge.common.MinecraftForge;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.client.registry.ClientRegistry;
import net.minecraftforge.fml.common.Mod;
import net.minecraftforge.fml.event.lifecycle.FMLClientSetupEvent;
import net.minecraftforge.fml.event.lifecycle.FMLCommonSetupEvent;
import net.minecraftforge.fml.event.server.FMLServerStartingEvent;
import net.minecraftforge.fml.javafmlmod.FMLJavaModLoadingContext;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.lwjgl.glfw.GLFW;

import com.randomevents.events.RandomEventManager;
import com.randomevents.client.gui.RandomEventsScreen;
import com.randomevents.client.ClientEventHandler;
import com.randomevents.commands.RandomEventsCommand;

@Mod("randomevents")
public class RandomEventsMod {
    public static final String MOD_ID = "randomevents";
    public static final Logger LOGGER = LogManager.getLogger();
    
    public static RandomEventManager eventManager;
    
    @OnlyIn(Dist.CLIENT)
    public static KeyBinding openGuiKey;

    public RandomEventsMod() {
        FMLJavaModLoadingContext.get().getModEventBus().addListener(this::setup);
        FMLJavaModLoadingContext.get().getModEventBus().addListener(this::doClientStuff);
        
        MinecraftForge.EVENT_BUS.register(this);
    }

    private void setup(final FMLCommonSetupEvent event) {
        LOGGER.info("Random Events Mod - Common Setup");
        eventManager = new RandomEventManager();
    }

    private void doClientStuff(final FMLClientSetupEvent event) {
        LOGGER.info("Random Events Mod - Client Setup");
        
        openGuiKey = new KeyBinding(
            "key.randomevents.open_gui",
            InputMappings.Type.KEYSYM,
            GLFW.GLFW_KEY_R,
            "key.categories.randomevents"
        );
        
        ClientRegistry.registerKeyBinding(openGuiKey);
        MinecraftForge.EVENT_BUS.register(new ClientEventHandler());
    }

    @SubscribeEvent
    public void onServerStarting(FMLServerStartingEvent event) {
        LOGGER.info("Random Events Mod - Server Starting");
        if (eventManager != null) {
            eventManager.onServerStart();
        }

        // Register commands
        RandomEventsCommand.register(event.getServer().getCommands().getDispatcher());
    }
}
