package com.randomevents.events.impl;

import com.randomevents.events.RandomEvent;
import net.minecraft.entity.player.ServerPlayerEntity;
import net.minecraft.item.ItemStack;
import net.minecraft.util.text.StringTextComponent;
import net.minecraft.util.text.TextFormatting;
import net.minecraft.world.server.ServerWorld;

import java.util.*;
import java.util.concurrent.ThreadLocalRandom;

public class InventoryRouletteEvent extends RandomEvent {
    
    public InventoryRouletteEvent() {
        super("inventory_roulette", "Inventory Roulette", "Everyone's inventories get shuffled between players!", EventType.BAD, 6);
    }
    
    @Override
    public void execute(ServerPlayerEntity player, ServerWorld world) {
        List<ServerPlayerEntity> allPlayers = world.getServer().getPlayerList().getPlayers();
        
        if (allPlayers.size() < 2) {
            return; // Not enough players, silently fail
        }
        
        // Collect all inventories
        Map<ServerPlayerEntity, List<ItemStack>> playerInventories = new HashMap<>();
        
        for (ServerPlayerEntity p : allPlayers) {
            List<ItemStack> inventory = new ArrayList<>();
            
            // Save main inventory (excluding armor and offhand)
            for (int i = 0; i < p.inventory.getContainerSize(); i++) {
                ItemStack stack = p.inventory.getItem(i);
                inventory.add(stack.copy());
                p.inventory.setItem(i, ItemStack.EMPTY);
            }
            
            playerInventories.put(p, inventory);

            // Show title at top of screen
            p.connection.send(new net.minecraft.network.play.server.STitlePacket(
                net.minecraft.network.play.server.STitlePacket.Type.TITLE,
                new StringTextComponent("Inventory Roulette").withStyle(TextFormatting.GOLD)
            ));
        }
        
        // Create a shuffled list of players
        List<ServerPlayerEntity> shuffledPlayers = new ArrayList<>(allPlayers);
        Collections.shuffle(shuffledPlayers, ThreadLocalRandom.current());
        
        // Redistribute inventories
        for (int i = 0; i < allPlayers.size(); i++) {
            ServerPlayerEntity originalPlayer = allPlayers.get(i);
            ServerPlayerEntity newOwner = shuffledPlayers.get(i);
            
            List<ItemStack> inventory = playerInventories.get(originalPlayer);
            
            // Give inventory to new owner
            for (int slot = 0; slot < Math.min(inventory.size(), newOwner.inventory.getContainerSize()); slot++) {
                ItemStack stack = inventory.get(slot);
                if (!stack.isEmpty()) {
                    if (!newOwner.inventory.add(stack)) {
                        // If inventory is full, drop the item
                        newOwner.drop(stack, false);
                    }
                }
            }
            
            // No messages about inventory changes
        }
    }
}
