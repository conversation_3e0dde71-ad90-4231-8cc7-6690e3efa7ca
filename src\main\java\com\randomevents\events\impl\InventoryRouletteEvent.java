package com.randomevents.events.impl;

import com.randomevents.events.RandomEvent;
import net.minecraft.entity.player.ServerPlayerEntity;
import net.minecraft.item.ItemStack;
import net.minecraft.util.text.StringTextComponent;
import net.minecraft.util.text.TextFormatting;
import net.minecraft.world.server.ServerWorld;

import java.util.*;
import java.util.concurrent.ThreadLocalRandom;

public class InventoryRouletteEvent extends RandomEvent {
    
    public InventoryRouletteEvent() {
        super("inventory_roulette", "Inventory Roulette", "Everyone's inventories get shuffled between players!", EventType.BAD, 6);
    }
    
    @Override
    public void execute(ServerPlayerEntity player, ServerWorld world) {
        List<ServerPlayerEntity> allPlayers = world.getServer().getPlayerList().getPlayers();
        
        if (allPlayers.size() < 2) {
            player.sendMessage(
                new StringTextComponent("Not enough players for Inventory Roulette!")
                    .withStyle(TextFormatting.RED), 
                player.getUUID()
            );
            return;
        }
        
        // Collect all inventories
        Map<ServerPlayerEntity, List<ItemStack>> playerInventories = new HashMap<>();
        
        for (ServerPlayerEntity p : allPlayers) {
            List<ItemStack> inventory = new ArrayList<>();
            
            // Save main inventory (excluding armor and offhand)
            for (int i = 0; i < p.inventory.getContainerSize(); i++) {
                ItemStack stack = p.inventory.getItem(i);
                inventory.add(stack.copy());
                p.inventory.setItem(i, ItemStack.EMPTY);
            }
            
            playerInventories.put(p, inventory);
            
            p.sendMessage(
                new StringTextComponent("INVENTORY ROULETTE: Your inventory has been shuffled with other players!")
                    .withStyle(TextFormatting.GOLD), 
                p.getUUID()
            );
        }
        
        // Create a shuffled list of players
        List<ServerPlayerEntity> shuffledPlayers = new ArrayList<>(allPlayers);
        Collections.shuffle(shuffledPlayers, ThreadLocalRandom.current());
        
        // Redistribute inventories
        for (int i = 0; i < allPlayers.size(); i++) {
            ServerPlayerEntity originalPlayer = allPlayers.get(i);
            ServerPlayerEntity newOwner = shuffledPlayers.get(i);
            
            List<ItemStack> inventory = playerInventories.get(originalPlayer);
            
            // Give inventory to new owner
            for (int slot = 0; slot < Math.min(inventory.size(), newOwner.inventory.getContainerSize()); slot++) {
                ItemStack stack = inventory.get(slot);
                if (!stack.isEmpty()) {
                    if (!newOwner.inventory.add(stack)) {
                        // If inventory is full, drop the item
                        newOwner.drop(stack, false);
                    }
                }
            }
            
            if (!originalPlayer.equals(newOwner)) {
                newOwner.sendMessage(
                    new StringTextComponent("You received " + originalPlayer.getName().getString() + "'s inventory!")
                        .withStyle(TextFormatting.YELLOW), 
                    newOwner.getUUID()
                );
            } else {
                newOwner.sendMessage(
                    new StringTextComponent("You kept your own inventory this time!")
                        .withStyle(TextFormatting.GREEN), 
                    newOwner.getUUID()
                );
            }
        }
    }
}
