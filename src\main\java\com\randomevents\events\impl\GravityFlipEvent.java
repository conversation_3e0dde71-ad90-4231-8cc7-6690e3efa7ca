package com.randomevents.events.impl;

import com.randomevents.events.RandomEvent;
import net.minecraft.entity.player.ServerPlayerEntity;
import net.minecraft.potion.EffectInstance;
import net.minecraft.potion.Effects;
import net.minecraft.util.text.StringTextComponent;
import net.minecraft.util.text.TextFormatting;
import net.minecraft.world.server.ServerWorld;
import net.minecraftforge.common.MinecraftForge;
import net.minecraftforge.event.TickEvent;
import net.minecraftforge.eventbus.api.SubscribeEvent;

import java.util.*;

public class GravityFlipEvent extends RandomEvent {
    private static final Map<UUID, GravityFlipInstance> activeInstances = new HashMap<>();
    
    public GravityFlipEvent() {
        super("gravity_flip", "Gravity Chaos", "Gravity becomes unpredictable - hold onto something!", EventType.BAD, 6);
    }
    
    @Override
    public void execute(ServerPlayerEntity player, ServerWorld world) {
        List<ServerPlayerEntity> allPlayers = world.getServer().getPlayerList().getPlayers();
        
        for (ServerPlayerEntity p : allPlayers) {
            p.sendMessage(
                new StringTextComponent("GRAVITY CHAOS: Gravity will randomly flip for 45 seconds!")
                    .withStyle(TextFormatting.DARK_PURPLE), 
                p.getUUID()
            );
            
            // Add levitation effect initially
            p.addEffect(new EffectInstance(Effects.LEVITATION, 900, 0)); // 45 seconds
            
            GravityFlipInstance instance = new GravityFlipInstance(p, world);
            activeInstances.put(p.getUUID(), instance);
        }
        
        // Register the tick handler if not already registered
        if (!activeInstances.isEmpty()) {
            MinecraftForge.EVENT_BUS.register(new GravityFlipTickHandler());
        }
    }
    
    public static class GravityFlipInstance {
        private final UUID playerUUID;
        private final ServerWorld world;
        private int ticksRemaining;
        private int nextFlipTick;
        private boolean isReversed;
        private int flipCounter;
        
        public GravityFlipInstance(ServerPlayerEntity player, ServerWorld world) {
            this.playerUUID = player.getUUID();
            this.world = world;
            this.ticksRemaining = 900; // 45 seconds
            this.nextFlipTick = 60 + (int)(Math.random() * 120); // 3-9 seconds
            this.isReversed = false;
            this.flipCounter = 0;
        }
        
        public boolean tick() {
            ticksRemaining--;
            nextFlipTick--;
            
            ServerPlayerEntity player = world.getServer().getPlayerList().getPlayer(playerUUID);
            if (player == null || !player.isAlive()) {
                return false;
            }
            
            if (ticksRemaining <= 0) {
                // Event completed - restore normal gravity
                player.removeEffect(Effects.LEVITATION);
                player.sendMessage(
                    new StringTextComponent("Gravity Chaos ended! Gravity is back to normal.")
                        .withStyle(TextFormatting.GREEN), 
                    playerUUID
                );
                return false;
            }
            
            if (nextFlipTick <= 0) {
                flipGravity(player);
                nextFlipTick = 40 + (int)(Math.random() * 80); // 2-6 seconds until next flip
                flipCounter++;
            }
            
            // Send periodic warnings
            if (nextFlipTick == 20) { // 1 second warning
                player.sendMessage(
                    new StringTextComponent("Gravity flip incoming in 1 second!")
                        .withStyle(TextFormatting.YELLOW), 
                    playerUUID
                );
            }
            
            return true;
        }
        
        private void flipGravity(ServerPlayerEntity player) {
            player.removeEffect(Effects.LEVITATION);
            
            if (isReversed) {
                // Normal gravity (let them fall)
                player.sendMessage(
                    new StringTextComponent("Gravity: DOWN!")
                        .withStyle(TextFormatting.RED), 
                    playerUUID
                );
                isReversed = false;
            } else {
                // Reverse gravity (levitation)
                int strength = (int)(Math.random() * 3); // 0-2 strength
                player.addEffect(new EffectInstance(Effects.LEVITATION, 100, strength));
                player.sendMessage(
                    new StringTextComponent("Gravity: UP!")
                        .withStyle(TextFormatting.BLUE), 
                    playerUUID
                );
                isReversed = true;
            }
        }
    }
    
    public static class GravityFlipTickHandler {
        @SubscribeEvent
        public void onServerTick(TickEvent.ServerTickEvent event) {
            if (event.phase != TickEvent.Phase.END) return;
            
            Iterator<Map.Entry<UUID, GravityFlipInstance>> iterator = activeInstances.entrySet().iterator();
            
            while (iterator.hasNext()) {
                Map.Entry<UUID, GravityFlipInstance> entry = iterator.next();
                
                if (!entry.getValue().tick()) {
                    iterator.remove();
                }
            }
            
            // Unregister if no active instances
            if (activeInstances.isEmpty()) {
                MinecraftForge.EVENT_BUS.unregister(this);
            }
        }
    }
}
