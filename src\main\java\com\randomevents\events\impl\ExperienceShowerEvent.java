package com.randomevents.events.impl;

import com.randomevents.events.RandomEvent;
import net.minecraft.entity.player.ServerPlayerEntity;
import net.minecraft.world.server.ServerWorld;

import java.util.concurrent.ThreadLocalRandom;

public class ExperienceShowerEvent extends RandomEvent {
    
    public ExperienceShowerEvent() {
        super("experience_shower", "Experience Shower", "Gain a burst of experience points!", EventType.GOOD, 8);
    }
    
    @Override
    public void execute(ServerPlayerEntity player, ServerWorld world) {
        int experienceAmount = ThreadLocalRandom.current().nextInt(500, 1500); // 500-1499 XP
        player.giveExperiencePoints(experienceAmount);
    }
}
