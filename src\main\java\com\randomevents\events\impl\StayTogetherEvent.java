package com.randomevents.events.impl;

import com.randomevents.events.RandomEvent;
import net.minecraft.entity.player.ServerPlayerEntity;
import net.minecraft.potion.EffectInstance;
import net.minecraft.potion.Effects;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.vector.Vector3d;
import net.minecraft.util.text.StringTextComponent;
import net.minecraft.util.text.TextFormatting;
import net.minecraft.world.server.ServerWorld;
import net.minecraftforge.common.MinecraftForge;
import net.minecraftforge.event.TickEvent;
import net.minecraftforge.eventbus.api.SubscribeEvent;

import java.util.*;
import java.util.concurrent.ThreadLocalRandom;

public class StayTogetherEvent extends RandomEvent {
    private static final Map<UUID, StayTogetherInstance> activeInstances = new HashMap<>();
    
    public StayTogetherEvent() {
        super("stay_together", "Unity or Death", "All players must stay within 7 blocks of each other or suffer!", EventType.BAD, 8);
    }
    
    @Override
    public void execute(ServerPlayerEntity player, ServerWorld world) {
        List<ServerPlayerEntity> allPlayers = world.getServer().getPlayerList().getPlayers();
        if (allPlayers.size() < 2) {
            player.sendMessage(new StringTextComponent("Not enough players for Unity or Death event!").withStyle(TextFormatting.RED), player.getUUID());
            return;
        }
        
        // Find center point of all players
        Vector3d centerPos = calculateCenterPosition(allPlayers);
        
        // Teleport all players to the center area
        for (ServerPlayerEntity p : allPlayers) {
            double offsetX = ThreadLocalRandom.current().nextDouble(-3, 3);
            double offsetZ = ThreadLocalRandom.current().nextDouble(-3, 3);
            p.teleportTo(centerPos.x + offsetX, centerPos.y + 1, centerPos.z + offsetZ);

            // Show title at top of screen
            p.connection.send(new net.minecraft.network.play.server.STitlePacket(
                net.minecraft.network.play.server.STitlePacket.Type.TITLE,
                new StringTextComponent("Unity or Death").withStyle(TextFormatting.DARK_RED)
            ));

            // Add glowing effect so players can see each other
            p.addEffect(new EffectInstance(Effects.GLOWING, 600, 0)); // 30 seconds
        }
        
        // Create and register the event instance
        StayTogetherInstance instance = new StayTogetherInstance(allPlayers, world);
        for (ServerPlayerEntity p : allPlayers) {
            activeInstances.put(p.getUUID(), instance);
        }
        
        // Register the tick handler if not already registered
        if (activeInstances.size() == allPlayers.size()) {
            MinecraftForge.EVENT_BUS.register(new StayTogetherTickHandler());
        }
    }
    
    private Vector3d calculateCenterPosition(List<ServerPlayerEntity> players) {
        double totalX = 0, totalY = 0, totalZ = 0;
        for (ServerPlayerEntity p : players) {
            totalX += p.getX();
            totalY += p.getY();
            totalZ += p.getZ();
        }
        return new Vector3d(totalX / players.size(), totalY / players.size(), totalZ / players.size());
    }
    
    public static class StayTogetherInstance {
        private final Set<UUID> playerUUIDs;
        private final ServerWorld world;
        private int ticksRemaining;
        private int damageTickCounter;
        
        public StayTogetherInstance(List<ServerPlayerEntity> players, ServerWorld world) {
            this.playerUUIDs = new HashSet<>();
            for (ServerPlayerEntity p : players) {
                this.playerUUIDs.add(p.getUUID());
            }
            this.world = world;
            this.ticksRemaining = 600; // 30 seconds
            this.damageTickCounter = 0;
        }
        
        public boolean tick() {
            ticksRemaining--;
            
            if (ticksRemaining <= 0) {
                // Event completed successfully - no messages
                return false; // Remove this instance
            }
            
            // Check if players are within range
            List<ServerPlayerEntity> alivePlayers = new ArrayList<>();
            for (UUID uuid : playerUUIDs) {
                ServerPlayerEntity player = world.getServer().getPlayerList().getPlayer(uuid);
                if (player != null && player.isAlive()) {
                    alivePlayers.add(player);
                }
            }
            
            if (alivePlayers.size() < 2) {
                return false; // Not enough players, end event
            }
            
            boolean allWithinRange = true;
            for (int i = 0; i < alivePlayers.size(); i++) {
                for (int j = i + 1; j < alivePlayers.size(); j++) {
                    double distance = alivePlayers.get(i).distanceTo(alivePlayers.get(j));
                    if (distance > 7.0) {
                        allWithinRange = false;
                        break;
                    }
                }
                if (!allWithinRange) break;
            }
            
            if (!allWithinRange) {
                damageTickCounter++;
                if (damageTickCounter >= 20) { // Every second
                    damageTickCounter = 0;
                    for (ServerPlayerEntity player : alivePlayers) {
                        player.hurt(net.minecraft.util.DamageSource.MAGIC, 1.0f); // Half heart
                    }
                }
            } else {
                damageTickCounter = 0;
            }
            
            // No periodic updates - removed chat messages
            
            return true; // Continue
        }
        
        public Set<UUID> getPlayerUUIDs() {
            return playerUUIDs;
        }
    }
    
    public static class StayTogetherTickHandler {
        @SubscribeEvent
        public void onServerTick(TickEvent.ServerTickEvent event) {
            if (event.phase != TickEvent.Phase.END) return;
            
            Iterator<Map.Entry<UUID, StayTogetherInstance>> iterator = activeInstances.entrySet().iterator();
            Set<StayTogetherInstance> processedInstances = new HashSet<>();
            
            while (iterator.hasNext()) {
                Map.Entry<UUID, StayTogetherInstance> entry = iterator.next();
                StayTogetherInstance instance = entry.getValue();
                
                if (!processedInstances.contains(instance)) {
                    processedInstances.add(instance);
                    
                    if (!instance.tick()) {
                        // Remove all players from this instance
                        Set<UUID> playersToRemove = new HashSet<>(instance.getPlayerUUIDs());
                        activeInstances.entrySet().removeIf(e -> playersToRemove.contains(e.getKey()));
                    }
                }
            }
            
            // Unregister if no active instances
            if (activeInstances.isEmpty()) {
                MinecraftForge.EVENT_BUS.unregister(this);
            }
        }
    }
}
