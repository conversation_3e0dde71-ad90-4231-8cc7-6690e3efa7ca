package com.randomevents.events.impl;

import com.randomevents.events.RandomEvent;
import net.minecraft.entity.player.ServerPlayerEntity;
import net.minecraft.item.ItemStack;
import net.minecraft.item.Items;
import net.minecraft.world.server.ServerWorld;

import java.util.concurrent.ThreadLocalRandom;

public class LuckyFindEvent extends RandomEvent {
    
    public LuckyFindEvent() {
        super("lucky_find", "Lucky Find", "Discover rare items in your inventory!", EventType.GOOD, 7);
    }
    
    @Override
    public void execute(ServerPlayerEntity player, ServerWorld world) {
        ItemStack luckyItem;
        
        double chance = ThreadLocalRandom.current().nextDouble();
        if (chance < 0.1) { // 10% - Very rare
            luckyItem = new ItemStack(Items.NETHERITE_INGOT, ThreadLocalRandom.current().nextInt(1, 3));
        } else if (chance < 0.3) { // 20% - Rare
            luckyItem = new ItemStack(Items.EMERALD, ThreadLocalRandom.current().nextInt(3, 8));
        } else if (chance < 0.6) { // 30% - Uncommon
            luckyItem = new ItemStack(Items.GOLD_INGOT, ThreadLocalRandom.current().nextInt(5, 12));
        } else { // 40% - Common
            luckyItem = new ItemStack(Items.IRON_INGOT, ThreadLocalRandom.current().nextInt(8, 16));
        }
        
        if (!player.addItem(luckyItem)) {
            player.drop(luckyItem, false);
        }
    }
}
