package com.randomevents.events.impl;

import com.randomevents.events.RandomEvent;
import net.minecraft.entity.player.ServerPlayerEntity;
import net.minecraft.potion.EffectInstance;
import net.minecraft.potion.Effects;
import net.minecraft.world.server.ServerWorld;

public class SlownessEvent extends RandomEvent {
    
    public SlownessEvent() {
        super("slowness", "Slowness Curse", "You move incredibly slowly!", EventType.BAD, 10);
    }
    
    @Override
    public void execute(ServerPlayerEntity player, ServerWorld world) {
        // Slowness II for 2 minutes (2400 ticks)
        EffectInstance slowness = new EffectInstance(Effects.MOVEMENT_SLOWDOWN, 2400, 1);
        player.addEffect(slowness);
    }
}
