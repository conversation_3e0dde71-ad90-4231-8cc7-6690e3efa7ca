package com.randomevents.events.impl;

import com.randomevents.events.RandomEvent;
import net.minecraft.entity.player.ServerPlayerEntity;
import net.minecraft.potion.EffectInstance;
import net.minecraft.potion.Effects;
import net.minecraft.world.server.ServerWorld;

public class NightVisionEvent extends RandomEvent {
    
    public NightVisionEvent() {
        super("night_vision", "Night Vision", "See clearly in the dark!", EventType.GOOD, 8);
    }
    
    @Override
    public void execute(ServerPlayerEntity player, ServerWorld world) {
        // Night Vision for 10 minutes (12000 ticks)
        EffectInstance nightVision = new EffectInstance(Effects.NIGHT_VISION, 12000, 0);
        player.addEffect(nightVision);
    }
}
