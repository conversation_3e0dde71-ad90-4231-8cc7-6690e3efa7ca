package com.randomevents.events.impl;

import com.randomevents.events.RandomEvent;
import net.minecraft.entity.EntityType;
import net.minecraft.entity.monster.CreeperEntity;
import net.minecraft.entity.player.ServerPlayerEntity;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.text.StringTextComponent;
import net.minecraft.util.text.TextFormatting;
import net.minecraft.world.server.ServerWorld;

import java.util.List;
import java.util.concurrent.ThreadLocalRandom;

public class CreeperSurpriseEvent extends RandomEvent {
    
    public CreeperSurpriseEvent() {
        super("creeper_surprise", "Creeper Surprise", "Creepers appear nearby - watch out!", EventType.BAD, 6);
    }
    
    @Override
    public void execute(ServerPlayerEntity player, ServerWorld world) {
        List<ServerPlayerEntity> allPlayers = world.getServer().getPlayerList().getPlayers();

        for (ServerPlayerEntity p : allPlayers) {
            // Show title at top of screen
            p.connection.send(new net.minecraft.network.play.server.STitlePacket(
                net.minecraft.network.play.server.STitlePacket.Type.TITLE,
                new StringTextComponent("Creeper Surprise").withStyle(TextFormatting.GREEN)
            ));

            BlockPos playerPos = p.blockPosition();
            int creeperCount = ThreadLocalRandom.current().nextInt(3, 6); // 3-5 creepers

            for (int i = 0; i < creeperCount; i++) {
                // Spawn creepers at various distances
                double angle = ThreadLocalRandom.current().nextDouble() * 2 * Math.PI;
                double radius = ThreadLocalRandom.current().nextDouble(6, 12);

                double x = playerPos.getX() + Math.cos(angle) * radius;
                double z = playerPos.getZ() + Math.sin(angle) * radius;
                double y = playerPos.getY();

                CreeperEntity creeper = new CreeperEntity(EntityType.CREEPER, world);
                creeper.setPos(x, y, z);
                creeper.setTarget(p);

                world.addFreshEntity(creeper);
            }
        }
    }
}
