package com.randomevents.events.impl;

import com.randomevents.events.RandomEvent;
import net.minecraft.entity.EntityType;
import net.minecraft.entity.monster.CreeperEntity;
import net.minecraft.entity.player.ServerPlayerEntity;
import net.minecraft.util.math.BlockPos;
import net.minecraft.world.server.ServerWorld;

import java.util.concurrent.ThreadLocalRandom;

public class CreeperSurpriseEvent extends RandomEvent {
    
    public CreeperSurpriseEvent() {
        super("creeper_surprise", "Creeper Surprise", "Creepers appear nearby - watch out!", EventType.BAD, 6);
    }
    
    @Override
    public void execute(ServerPlayerEntity player, ServerWorld world) {
        BlockPos playerPos = player.blockPosition();
        int creeperCount = ThreadLocalRandom.current().nextInt(3, 6); // 3-5 creepers
        
        for (int i = 0; i < creeperCount; i++) {
            // Spawn creepers at various distances
            double angle = ThreadLocalRandom.current().nextDouble() * 2 * Math.PI;
            double radius = ThreadLocalRandom.current().nextDouble(6, 12);
            
            double x = playerPos.getX() + Math.cos(angle) * radius;
            double z = playerPos.getZ() + Math.sin(angle) * radius;
            double y = playerPos.getY();
            
            CreeperEntity creeper = new CreeperEntity(EntityType.CREEPER, world);
            creeper.setPos(x, y, z);
            creeper.setTarget(player);
            
            // 20% chance for charged creeper
            if (ThreadLocalRandom.current().nextDouble() < 0.2) {
                // Use reflection or alternative method to make charged creeper
                // For now, just spawn a regular creeper - can be enhanced later
                // creeper.getEntityData().set(CreeperEntity.DATA_IS_POWERED, true);
            }
            
            world.addFreshEntity(creeper);
        }
    }
}
