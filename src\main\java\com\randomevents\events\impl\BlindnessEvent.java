package com.randomevents.events.impl;

import com.randomevents.events.RandomEvent;
import net.minecraft.entity.player.ServerPlayerEntity;
import net.minecraft.potion.EffectInstance;
import net.minecraft.potion.Effects;
import net.minecraft.world.server.ServerWorld;

public class BlindnessEvent extends RandomEvent {
    
    public BlindnessEvent() {
        super("blindness", "Blindness", "Darkness clouds your vision!", EventType.BAD, 8);
    }
    
    @Override
    public void execute(ServerPlayerEntity player, ServerWorld world) {
        // Blindness for 1 minute (1200 ticks)
        EffectInstance blindness = new EffectInstance(Effects.BLINDNESS, 1200, 0);
        player.addEffect(blindness);
    }
}
