package com.randomevents.events;

import net.minecraft.entity.player.ServerPlayerEntity;
import net.minecraft.util.text.ITextComponent;
import net.minecraft.util.text.StringTextComponent;
import net.minecraft.util.text.TextFormatting;
import net.minecraft.world.server.ServerWorld;

public abstract class RandomEvent {
    protected final String id;
    protected final String name;
    protected final String description;
    protected final EventType type;
    protected final int weight; // Higher weight = more likely to occur
    
    public RandomEvent(String id, String name, String description, EventType type, int weight) {
        this.id = id;
        this.name = name;
        this.description = description;
        this.type = type;
        this.weight = weight;
    }
    
    public abstract void execute(ServerPlayerEntity player, ServerWorld world);
    
    public String getId() {
        return id;
    }
    
    public String getName() {
        return name;
    }
    
    public String getDescription() {
        return description;
    }
    
    public EventType getType() {
        return type;
    }
    
    public int getWeight() {
        return weight;
    }
    
    public ITextComponent getFormattedName() {
        TextFormatting color = type == EventType.GOOD ? TextFormatting.GREEN : TextFormatting.RED;
        return new StringTextComponent(name).withStyle(color);
    }
    
    public ITextComponent getFormattedDescription() {
        return new StringTextComponent(description).withStyle(TextFormatting.GRAY);
    }
    
    public enum EventType {
        GOOD("Good Event", TextFormatting.GREEN),
        BAD("Bad Event", TextFormatting.RED);
        
        private final String displayName;
        private final TextFormatting color;
        
        EventType(String displayName, TextFormatting color) {
            this.displayName = displayName;
            this.color = color;
        }
        
        public String getDisplayName() {
            return displayName;
        }
        
        public TextFormatting getColor() {
            return color;
        }
    }
}
